<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="excel-live-server" Version="4.0.30" Publisher="local-smart" />
			<DisplayName>XVBA - Live Server VBA</DisplayName>
			<Description xml:space="preserve">Edit VBA files from Excel/Access In VSCode with Intellisense,Live Server, and Create Ribbons. You can use Snippets, Autocomplete (Language Server), run Macros from VSCode, add namespace to your vba files etc..  </Description>
			<Tags>Xvba,vba,excel,live Server,vb,vbs,macro,Visual Basic Application,VBA,Excel,Access,VBS,CATScript,__ext_cls,__ext_frm,__ext_bas,__ext_vbs,__ext_catscript</Tags>
			<Categories>Programming Languages,Snippets,Language Packs,Formatters,Debuggers,Testing,Visualization,Other</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.59.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/aeraphe/excel-autoload.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/aeraphe/excel-autoload.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/aeraphe/excel-autoload.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/aeraphe/excel-autoload/issues" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://github.com/aeraphe/excel-autoload#readme" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				
				
			</Properties>
			<License>extension/LICENSE.txt</License>
			<Icon>extension/resources/icon.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/README.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.Changelog" Path="extension/CHANGELOG.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.txt" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/resources/icon.png" Addressable="true" />
		</Assets>
	</PackageManifest>