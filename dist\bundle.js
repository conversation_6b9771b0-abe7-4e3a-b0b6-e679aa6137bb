module.exports=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=296)}([function(e,t){e.exports=require("path")},function(e,t){e.exports=require("fs")},,function(e,t){e.exports=require("os")},function(e,t,n){var r,i,o=n(1),s=n(116),a=n(118),c=n(119),u=n(6);function l(e,t){Object.defineProperty(e,r,{get:function(){return t}})}"function"==typeof Symbol&&"function"==typeof Symbol.for?(r=Symbol.for("graceful-fs.queue"),i=Symbol.for("graceful-fs.previous")):(r="___graceful-fs.queue",i="___graceful-fs.previous");var f=function(){};if(u.debuglog?f=u.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(f=function(){var e=u.format.apply(u,arguments);e="GFS4: "+e.split(/\n/).join("\nGFS4: "),console.error(e)}),!o[r]){var d=global[r]||[];l(o,d),o.close=function(e){function t(t,n){return e.call(o,t,(function(e){e||m(),"function"==typeof n&&n.apply(this,arguments)}))}return Object.defineProperty(t,i,{value:e}),t}(o.close),o.closeSync=function(e){function t(t){e.apply(o,arguments),m()}return Object.defineProperty(t,i,{value:e}),t}(o.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",(function(){f(o[r]),n(31).equal(o[r].length,0)}))}function h(e){s(e),e.gracefulify=h,e.createReadStream=function(t,n){return new e.ReadStream(t,n)},e.createWriteStream=function(t,n){return new e.WriteStream(t,n)};var t=e.readFile;e.readFile=function(e,n,r){return"function"==typeof n&&(r=n,n=null),function e(n,r,i){return t(n,r,(function(t){!t||"EMFILE"!==t.code&&"ENFILE"!==t.code?("function"==typeof i&&i.apply(this,arguments),m()):p([e,[n,r,i]])}))}(e,n,r)};var n=e.writeFile;e.writeFile=function(e,t,r,i){return"function"==typeof r&&(i=r,r=null),function e(t,r,i,o){return n(t,r,i,(function(n){!n||"EMFILE"!==n.code&&"ENFILE"!==n.code?("function"==typeof o&&o.apply(this,arguments),m()):p([e,[t,r,i,o]])}))}(e,t,r,i)};var r=e.appendFile;r&&(e.appendFile=function(e,t,n,i){return"function"==typeof n&&(i=n,n=null),function e(t,n,i,o){return r(t,n,i,(function(r){!r||"EMFILE"!==r.code&&"ENFILE"!==r.code?("function"==typeof o&&o.apply(this,arguments),m()):p([e,[t,n,i,o]])}))}(e,t,n,i)});var i=e.readdir;function o(t){return i.apply(e,t)}if(e.readdir=function(e,t,n){var r=[e];return"function"!=typeof t?r.push(t):n=t,r.push((function(e,t){t&&t.sort&&t.sort(),!e||"EMFILE"!==e.code&&"ENFILE"!==e.code?("function"==typeof n&&n.apply(this,arguments),m()):p([o,[r]])})),o(r)},"v0.8"===process.version.substr(0,4)){var c=a(e);g=c.ReadStream,y=c.WriteStream}var u=e.ReadStream;u&&(g.prototype=Object.create(u.prototype),g.prototype.open=function(){var e=this;b(e.path,e.flags,e.mode,(function(t,n){t?(e.autoClose&&e.destroy(),e.emit("error",t)):(e.fd=n,e.emit("open",n),e.read())}))});var l=e.WriteStream;l&&(y.prototype=Object.create(l.prototype),y.prototype.open=function(){var e=this;b(e.path,e.flags,e.mode,(function(t,n){t?(e.destroy(),e.emit("error",t)):(e.fd=n,e.emit("open",n))}))}),Object.defineProperty(e,"ReadStream",{get:function(){return g},set:function(e){g=e},enumerable:!0,configurable:!0}),Object.defineProperty(e,"WriteStream",{get:function(){return y},set:function(e){y=e},enumerable:!0,configurable:!0});var f=g;Object.defineProperty(e,"FileReadStream",{get:function(){return f},set:function(e){f=e},enumerable:!0,configurable:!0});var d=y;function g(e,t){return this instanceof g?(u.apply(this,arguments),this):g.apply(Object.create(g.prototype),arguments)}function y(e,t){return this instanceof y?(l.apply(this,arguments),this):y.apply(Object.create(y.prototype),arguments)}Object.defineProperty(e,"FileWriteStream",{get:function(){return d},set:function(e){d=e},enumerable:!0,configurable:!0});var v=e.open;function b(e,t,n,r){return"function"==typeof n&&(r=n,n=null),function e(t,n,r,i){return v(t,n,r,(function(o,s){!o||"EMFILE"!==o.code&&"ENFILE"!==o.code?("function"==typeof i&&i.apply(this,arguments),m()):p([e,[t,n,r,i]])}))}(e,t,n,r)}return e.open=b,e}function p(e){f("ENQUEUE",e[0].name,e[1]),o[r].push(e)}function m(){var e=o[r].shift();e&&(f("RETRY",e[0].name,e[1]),e[0].apply(null,e[1]))}global[r]||l(global,o[r]),e.exports=h(c(o)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!o.__patched&&(e.exports=h(o),o.__patched=!0)},,function(e,t){e.exports=require("util")},,function(e,t,n){"use strict";t.fromCallback=function(e){return Object.defineProperty((function(...t){if("function"!=typeof t[t.length-1])return new Promise((n,r)=>{e.call(this,...t,(e,t)=>null!=e?r(e):n(t))});e.apply(this,t)}),"name",{value:e.name})},t.fromPromise=function(e){return Object.defineProperty((function(...t){const n=t[t.length-1];if("function"!=typeof n)return e.apply(this,t);e.apply(this,t.slice(0,-1)).then(e=>n(null,e),n)}),"name",{value:e.name})}},function(e,t,n){"use strict";function r(e){for(var n in e)t.hasOwnProperty(n)||(t[n]=e[n])}Object.defineProperty(t,"__esModule",{value:!0});const i=n(12);t.ErrorCodes=i.ErrorCodes,t.ResponseError=i.ResponseError,t.CancellationToken=i.CancellationToken,t.CancellationTokenSource=i.CancellationTokenSource,t.Disposable=i.Disposable,t.Event=i.Event,t.Emitter=i.Emitter,t.Trace=i.Trace,t.TraceFormat=i.TraceFormat,t.SetTraceNotification=i.SetTraceNotification,t.LogTraceNotification=i.LogTraceNotification,t.RequestType=i.RequestType,t.RequestType0=i.RequestType0,t.NotificationType=i.NotificationType,t.NotificationType0=i.NotificationType0,t.MessageReader=i.MessageReader,t.MessageWriter=i.MessageWriter,t.ConnectionStrategy=i.ConnectionStrategy,t.StreamMessageReader=i.StreamMessageReader,t.StreamMessageWriter=i.StreamMessageWriter,t.IPCMessageReader=i.IPCMessageReader,t.IPCMessageWriter=i.IPCMessageWriter,t.createClientPipeTransport=i.createClientPipeTransport,t.createServerPipeTransport=i.createServerPipeTransport,t.generateRandomPipeName=i.generateRandomPipeName,t.createClientSocketTransport=i.createClientSocketTransport,t.createServerSocketTransport=i.createServerSocketTransport,t.ProgressType=i.ProgressType,r(n(206)),r(n(207));const o=n(218),s=n(219);!function(e){let t,n,r,i,a,c;!function(e){e.method=o.CallHierarchyPrepareRequest.method,e.type=o.CallHierarchyPrepareRequest.type}(t=e.CallHierarchyPrepareRequest||(e.CallHierarchyPrepareRequest={})),function(e){e.method=o.CallHierarchyIncomingCallsRequest.method,e.type=o.CallHierarchyIncomingCallsRequest.type}(n=e.CallHierarchyIncomingCallsRequest||(e.CallHierarchyIncomingCallsRequest={})),function(e){e.method=o.CallHierarchyOutgoingCallsRequest.method,e.type=o.CallHierarchyOutgoingCallsRequest.type}(r=e.CallHierarchyOutgoingCallsRequest||(e.CallHierarchyOutgoingCallsRequest={})),e.SemanticTokenTypes=s.SemanticTokenTypes,e.SemanticTokenModifiers=s.SemanticTokenModifiers,e.SemanticTokens=s.SemanticTokens,function(e){e.method=s.SemanticTokensRequest.method,e.type=s.SemanticTokensRequest.type}(i=e.SemanticTokensRequest||(e.SemanticTokensRequest={})),function(e){e.method=s.SemanticTokensEditsRequest.method,e.type=s.SemanticTokensEditsRequest.type}(a=e.SemanticTokensEditsRequest||(e.SemanticTokensEditsRequest={})),function(e){e.method=s.SemanticTokensRangeRequest.method,e.type=s.SemanticTokensRangeRequest.type}(c=e.SemanticTokensRangeRequest||(e.SemanticTokensRangeRequest={}))}(t.Proposed||(t.Proposed={})),t.createProtocolConnection=function(e,t,n,r){return i.createMessageConnection(e,t,n,r)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12);class i extends r.RequestType0{constructor(e){super(e)}}t.ProtocolRequestType0=i;class o extends r.RequestType{constructor(e){super(e)}}t.ProtocolRequestType=o;class s extends r.NotificationType{constructor(e){super(e)}}t.ProtocolNotificationType=s;class a extends r.NotificationType0{constructor(e){super(e)}}t.ProtocolNotificationType0=a},function(e,t,n){"use strict";const r=n(8).fromPromise,{makeDir:i,makeDirSync:o}=n(121),s=r(i);e.exports={mkdirs:s,mkdirsSync:o,mkdirp:s,mkdirpSync:o,ensureDir:s,ensureDirSync:o}},function(e,t,n){"use strict";function r(e){for(var n in e)t.hasOwnProperty(n)||(t[n]=e[n])}Object.defineProperty(t,"__esModule",{value:!0});const i=n(22),o=n(201);t.RequestType=o.RequestType,t.RequestType0=o.RequestType0,t.RequestType1=o.RequestType1,t.RequestType2=o.RequestType2,t.RequestType3=o.RequestType3,t.RequestType4=o.RequestType4,t.RequestType5=o.RequestType5,t.RequestType6=o.RequestType6,t.RequestType7=o.RequestType7,t.RequestType8=o.RequestType8,t.RequestType9=o.RequestType9,t.ResponseError=o.ResponseError,t.ErrorCodes=o.ErrorCodes,t.NotificationType=o.NotificationType,t.NotificationType0=o.NotificationType0,t.NotificationType1=o.NotificationType1,t.NotificationType2=o.NotificationType2,t.NotificationType3=o.NotificationType3,t.NotificationType4=o.NotificationType4,t.NotificationType5=o.NotificationType5,t.NotificationType6=o.NotificationType6,t.NotificationType7=o.NotificationType7,t.NotificationType8=o.NotificationType8,t.NotificationType9=o.NotificationType9;const s=n(47);t.MessageReader=s.MessageReader,t.StreamMessageReader=s.StreamMessageReader,t.IPCMessageReader=s.IPCMessageReader,t.SocketMessageReader=s.SocketMessageReader;const a=n(48);t.MessageWriter=a.MessageWriter,t.StreamMessageWriter=a.StreamMessageWriter,t.IPCMessageWriter=a.IPCMessageWriter,t.SocketMessageWriter=a.SocketMessageWriter;const c=n(35);t.Disposable=c.Disposable,t.Event=c.Event,t.Emitter=c.Emitter;const u=n(202);t.CancellationTokenSource=u.CancellationTokenSource,t.CancellationToken=u.CancellationToken;const l=n(203);var f,d,h,p,m,g,y,v;r(n(204)),r(n(205)),function(e){e.type=new o.NotificationType("$/cancelRequest")}(f||(f={})),function(e){e.type=new o.NotificationType("$/progress")}(d||(d={})),t.ProgressType=class{constructor(){}},t.NullLogger=Object.freeze({error:()=>{},warn:()=>{},info:()=>{},log:()=>{}}),function(e){e[e.Off=0]="Off",e[e.Messages=1]="Messages",e[e.Verbose=2]="Verbose"}(h=t.Trace||(t.Trace={})),function(e){e.fromString=function(t){if(!i.string(t))return e.Off;switch(t=t.toLowerCase()){case"off":return e.Off;case"messages":return e.Messages;case"verbose":return e.Verbose;default:return e.Off}},e.toString=function(t){switch(t){case e.Off:return"off";case e.Messages:return"messages";case e.Verbose:return"verbose";default:return"off"}}}(h=t.Trace||(t.Trace={})),function(e){e.Text="text",e.JSON="json"}(t.TraceFormat||(t.TraceFormat={})),function(e){e.fromString=function(t){return"json"===(t=t.toLowerCase())?e.JSON:e.Text}}(p=t.TraceFormat||(t.TraceFormat={})),function(e){e.type=new o.NotificationType("$/setTraceNotification")}(m=t.SetTraceNotification||(t.SetTraceNotification={})),function(e){e.type=new o.NotificationType("$/logTraceNotification")}(g=t.LogTraceNotification||(t.LogTraceNotification={})),function(e){e[e.Closed=1]="Closed",e[e.Disposed=2]="Disposed",e[e.AlreadyListening=3]="AlreadyListening"}(y=t.ConnectionErrors||(t.ConnectionErrors={}));class b extends Error{constructor(e,t){super(t),this.code=e,Object.setPrototypeOf(this,b.prototype)}}function _(e,t,n,r){let s=0,a=0,_=0;const w="2.0";let S,x,k,E,T=Object.create(null),R=Object.create(null),P=new Map,O=new l.LinkedMap,C=Object.create(null),D=Object.create(null),M=h.Off,N=p.Text,j=v.New,q=new c.Emitter,L=new c.Emitter,F=new c.Emitter,I=new c.Emitter,A=new c.Emitter;function W(e){return"req-"+e.toString()}function $(e){}function H(){return j===v.Listening}function z(){return j===v.Closed}function B(){return j===v.Disposed}function U(){j!==v.New&&j!==v.Listening||(j=v.Closed,L.fire(void 0))}e.onClose(U),e.onError((function(e){q.fire([e,void 0,void 0])})),t.onClose(U),t.onError((function(e){q.fire(e)}));let K=e=>{try{if(o.isNotificationMessage(e)&&e.method===f.type.method){let n=W(e.params.id),i=O.get(n);if(o.isRequestMessage(i)){let o=r&&r.cancelUndispatched?r.cancelUndispatched(i,$):void 0;if(o&&(void 0!==o.error||void 0!==o.result))return O.delete(n),o.id=i.id,J(o,e.method,Date.now()),void t.write(o)}}!function(e,t){var n;o.isRequestMessage(t)?e.set(W(t.id),t):o.isResponseMessage(t)?e.set(null===(n=t.id)?"res-unknown-"+(++_).toString():"res-"+n.toString(),t):e.set("not-"+(++a).toString(),t)}(O,e)}finally{!function e(){k||0===O.size||(k=setImmediate(()=>{k=void 0,function(){if(0===O.size)return;let r=O.shift();try{o.isRequestMessage(r)?function(e){if(B())return;function n(n,r,i){let s={jsonrpc:w,id:e.id};n instanceof o.ResponseError?s.error=n.toJson():s.result=void 0===n?null:n,J(s,r,i),t.write(s)}function r(n,r,i){let o={jsonrpc:w,id:e.id,error:n.toJson()};J(o,r,i),t.write(o)}function s(n,r,i){void 0===n&&(n=null);let o={jsonrpc:w,id:e.id,result:n};J(o,r,i),t.write(o)}!function(e){if(M!==h.Off&&E)if(N===p.Text){let t;M===h.Verbose&&e.params&&(t=`Params: ${JSON.stringify(e.params,null,4)}\n\n`),E.log(`Received request '${e.method} - (${e.id})'.`,t)}else V("receive-request",e)}(e);let a,c,l=T[e.method];l&&(a=l.type,c=l.handler);let f=Date.now();if(c||S){let t=new u.CancellationTokenSource,l=String(e.id);D[l]=t;try{let u;u=void 0===e.params||void 0!==a&&0===a.numberOfParams?c?c(t.token):S(e.method,t.token):i.array(e.params)&&(void 0===a||a.numberOfParams>1)?c?c(...e.params,t.token):S(e.method,...e.params,t.token):c?c(e.params,t.token):S(e.method,e.params,t.token);let d=u;u?d.then?d.then(t=>{delete D[l],n(t,e.method,f)},t=>{delete D[l],t instanceof o.ResponseError?r(t,e.method,f):t&&i.string(t.message)?r(new o.ResponseError(o.ErrorCodes.InternalError,`Request ${e.method} failed with message: ${t.message}`),e.method,f):r(new o.ResponseError(o.ErrorCodes.InternalError,`Request ${e.method} failed unexpectedly without providing any details.`),e.method,f)}):(delete D[l],n(u,e.method,f)):(delete D[l],s(u,e.method,f))}catch(t){delete D[l],t instanceof o.ResponseError?n(t,e.method,f):t&&i.string(t.message)?r(new o.ResponseError(o.ErrorCodes.InternalError,`Request ${e.method} failed with message: ${t.message}`),e.method,f):r(new o.ResponseError(o.ErrorCodes.InternalError,`Request ${e.method} failed unexpectedly without providing any details.`),e.method,f)}}else r(new o.ResponseError(o.ErrorCodes.MethodNotFound,"Unhandled method "+e.method),e.method,f)}(r):o.isNotificationMessage(r)?function(e){if(B())return;let t,r;if(e.method===f.type.method)r=e=>{let t=e.id,n=D[String(t)];n&&n.cancel()};else{let n=R[e.method];n&&(r=n.handler,t=n.type)}if(r||x)try{!function(e){if(M!==h.Off&&E&&e.method!==g.type.method)if(N===p.Text){let t;M===h.Verbose&&(t=e.params?`Params: ${JSON.stringify(e.params,null,4)}\n\n`:"No parameters provided.\n\n"),E.log(`Received notification '${e.method}'.`,t)}else V("receive-notification",e)}(e),void 0===e.params||void 0!==t&&0===t.numberOfParams?r?r():x(e.method):i.array(e.params)&&(void 0===t||t.numberOfParams>1)?r?r(...e.params):x(e.method,...e.params):r?r(e.params):x(e.method,e.params)}catch(t){t.message?n.error(`Notification handler '${e.method}' failed with message: ${t.message}`):n.error(`Notification handler '${e.method}' failed unexpectedly.`)}else F.fire(e)}(r):o.isResponseMessage(r)?function(e){if(!B())if(null===e.id)e.error?n.error("Received response message without id: Error is: \n"+JSON.stringify(e.error,void 0,4)):n.error("Received response message without id. No further error information provided.");else{let t=String(e.id),r=C[t];if(function(e,t){if(M!==h.Off&&E)if(N===p.Text){let n;if(M===h.Verbose&&(e.error&&e.error.data?n=`Error data: ${JSON.stringify(e.error.data,null,4)}\n\n`:e.result?n=`Result: ${JSON.stringify(e.result,null,4)}\n\n`:void 0===e.error&&(n="No result returned.\n\n")),t){let r=e.error?` Request failed: ${e.error.message} (${e.error.code}).`:"";E.log(`Received response '${t.method} - (${e.id})' in ${Date.now()-t.timerStart}ms.${r}`,n)}else E.log(`Received response ${e.id} without active response promise.`,n)}else V("receive-response",e)}(e,r),r){delete C[t];try{if(e.error){let t=e.error;r.reject(new o.ResponseError(t.code,t.message,t.data))}else{if(void 0===e.result)throw new Error("Should never happen.");r.resolve(e.result)}}catch(e){e.message?n.error(`Response handler '${r.method}' failed with message: ${e.message}`):n.error(`Response handler '${r.method}' failed unexpectedly.`)}}}}(r):function(e){if(!e)return void n.error("Received empty message.");n.error("Received message which is neither a response nor a notification message:\n"+JSON.stringify(e,null,4));let t=e;if(i.string(t.id)||i.number(t.id)){let e=String(t.id),n=C[e];n&&n.reject(new Error("The received response has neither a result nor an error property."))}}(r)}finally{e()}}()}))}()}};function J(e,t,n){if(M!==h.Off&&E)if(N===p.Text){let r;M===h.Verbose&&(e.error&&e.error.data?r=`Error data: ${JSON.stringify(e.error.data,null,4)}\n\n`:e.result?r=`Result: ${JSON.stringify(e.result,null,4)}\n\n`:void 0===e.error&&(r="No result returned.\n\n")),E.log(`Sending response '${t} - (${e.id})'. Processing request took ${Date.now()-n}ms`,r)}else V("send-response",e)}function V(e,t){if(!E||M===h.Off)return;const n={isLSPMessage:!0,type:e,message:t,timestamp:Date.now()};E.log(n)}function G(){if(z())throw new b(y.Closed,"Connection is closed.");if(B())throw new b(y.Disposed,"Connection is disposed.")}function Y(e){return void 0===e?null:e}function Z(e,t){let n,r=e.numberOfParams;switch(r){case 0:n=null;break;case 1:n=Y(t[0]);break;default:n=[];for(let e=0;e<t.length&&e<r;e++)n.push(Y(t[e]));if(t.length<r)for(let e=t.length;e<r;e++)n.push(null)}return n}let Q={sendNotification:(e,...n)=>{let r,o;if(G(),i.string(e))switch(r=e,n.length){case 0:o=null;break;case 1:o=n[0];break;default:o=n}else r=e.method,o=Z(e,n);let s={jsonrpc:w,method:r,params:o};!function(e){if(M!==h.Off&&E)if(N===p.Text){let t;M===h.Verbose&&(t=e.params?`Params: ${JSON.stringify(e.params,null,4)}\n\n`:"No parameters provided.\n\n"),E.log(`Sending notification '${e.method}'.`,t)}else V("send-notification",e)}(s),t.write(s)},onNotification:(e,t)=>{G(),i.func(e)?x=e:t&&(i.string(e)?R[e]={type:void 0,handler:t}:R[e.method]={type:e,handler:t})},onProgress:(e,t,n)=>{if(P.has(t))throw new Error(`Progress handler for token ${t} already registered`);return P.set(t,n),{dispose:()=>{P.delete(t)}}},sendProgress:(e,t,n)=>{Q.sendNotification(d.type,{token:t,value:n})},onUnhandledProgress:I.event,sendRequest:(e,...n)=>{let r,a,c;if(G(),function(){if(!H())throw new Error("Call listen() first.")}(),i.string(e))switch(r=e,n.length){case 0:a=null;break;case 1:u.CancellationToken.is(n[0])?(a=null,c=n[0]):a=Y(n[0]);break;default:const e=n.length-1;u.CancellationToken.is(n[e])?(c=n[e],a=2===n.length?Y(n[0]):n.slice(0,e).map(e=>Y(e))):a=n.map(e=>Y(e))}else{r=e.method,a=Z(e,n);let t=e.numberOfParams;c=u.CancellationToken.is(n[t])?n[t]:void 0}let l=s++,d=new Promise((e,n)=>{let i={jsonrpc:w,id:l,method:r,params:a},s={method:r,timerStart:Date.now(),resolve:e,reject:n};!function(e){if(M!==h.Off&&E)if(N===p.Text){let t;M===h.Verbose&&e.params&&(t=`Params: ${JSON.stringify(e.params,null,4)}\n\n`),E.log(`Sending request '${e.method} - (${e.id})'.`,t)}else V("send-request",e)}(i);try{t.write(i)}catch(e){s.reject(new o.ResponseError(o.ErrorCodes.MessageWriteError,e.message?e.message:"Unknown reason")),s=null}s&&(C[String(l)]=s)});return c&&c.onCancellationRequested(()=>{Q.sendNotification(f.type,{id:l})}),d},onRequest:(e,t)=>{G(),i.func(e)?S=e:t&&(i.string(e)?T[e]={type:void 0,handler:t}:T[e.method]={type:e,handler:t})},trace:(e,t,n)=>{let r=!1,o=p.Text;void 0!==n&&(i.boolean(n)?r=n:(r=n.sendNotification||!1,o=n.traceFormat||p.Text)),M=e,N=o,E=M===h.Off?void 0:t,!r||z()||B()||Q.sendNotification(m.type,{value:h.toString(e)})},onError:q.event,onClose:L.event,onUnhandledNotification:F.event,onDispose:A.event,dispose:()=>{if(B())return;j=v.Disposed,A.fire(void 0);let n=new Error("Connection got disposed.");Object.keys(C).forEach(e=>{C[e].reject(n)}),C=Object.create(null),D=Object.create(null),O=new l.LinkedMap,i.func(t.dispose)&&t.dispose(),i.func(e.dispose)&&e.dispose()},listen:()=>{G(),function(){if(H())throw new b(y.AlreadyListening,"Connection is already listening")}(),j=v.Listening,e.listen(K)},inspect:()=>{console.log("inspect")}};return Q.onNotification(g.type,e=>{M!==h.Off&&E&&E.log(e.message,M===h.Verbose?e.verbose:void 0)}),Q.onNotification(d.type,e=>{const t=P.get(e.token);t?t(e.value):I.fire(e)}),Q}t.ConnectionError=b,function(e){e.is=function(e){let t=e;return t&&i.func(t.cancelUndispatched)}}(t.ConnectionStrategy||(t.ConnectionStrategy={})),function(e){e[e.New=1]="New",e[e.Listening=2]="Listening",e[e.Closed=3]="Closed",e[e.Disposed=4]="Disposed"}(v||(v={})),t.createMessageConnection=function(e,n,r,i){var o;return r||(r=t.NullLogger),_(void 0!==(o=e).listen&&void 0===o.read?e:new s.StreamMessageReader(e),function(e){return void 0!==e.write&&void 0===e.end}(n)?n:new a.StreamMessageWriter(n),r,i)}},,,,,,function(e,t,n){"use strict";const r=n(8).fromPromise,i=n(30);e.exports={pathExists:r((function(e){return i.access(e).then(()=>!0).catch(()=>!1)})),pathExistsSync:i.existsSync}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(86).config(),process.env;const r=n(141)({level:"info"});t.default=r},,function(e,t,n){"use strict";const r=Symbol("pino.setLevel"),i=Symbol("pino.getLevel"),o=Symbol("pino.levelVal"),s=Symbol("pino.useLevelLabels"),a=Symbol("pino.useOnlyCustomLevels"),c=Symbol("pino.mixin"),u=Symbol("pino.lsCache"),l=Symbol("pino.chindings"),f=Symbol("pino.parsedChindings"),d=Symbol("pino.asJson"),h=Symbol("pino.write"),p=Symbol("pino.redactFmt"),m=Symbol("pino.time"),g=Symbol("pino.timeSliceIndex"),y=Symbol("pino.stream"),v=Symbol("pino.stringify"),b=Symbol("pino.stringifiers"),_=Symbol("pino.end"),w=Symbol("pino.formatOpts"),S=Symbol("pino.messageKey"),x=Symbol("pino.nestedKey"),k=Symbol("pino.wildcardFirst"),E=Symbol.for("pino.serializers"),T=Symbol.for("pino.formatters"),R=Symbol.for("pino.hooks"),P=Symbol.for("pino.metadata");e.exports={setLevelSym:r,getLevelSym:i,levelValSym:o,useLevelLabelsSym:s,mixinSym:c,lsCacheSym:u,chindingsSym:l,parsedChindingsSym:f,asJsonSym:d,writeSym:h,serializersSym:E,redactFmtSym:p,timeSym:m,timeSliceIndexSym:g,streamSym:y,stringifySym:v,stringifiersSym:b,endSym:_,formatOptsSym:w,messageKeySym:S,nestedKeySym:x,wildcardFirstSym:k,needsMetadataGsym:P,useOnlyCustomLevelsSym:a,formattersSym:T,hooksSym:R}},function(e,t,n){"use strict";function r(e){return"string"==typeof e||e instanceof String}function i(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.boolean=function(e){return!0===e||!1===e},t.string=r,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=i,t.stringArray=function(e){return i(e)&&e.every(e=>r(e))}},function(e,t){e.exports=require("events")},,,function(e,t){e.exports=i,i.default=i,i.stable=s,i.stableStringify=s;var n=[],r=[];function i(e,t,i){var o;for(function e(t,i,o,s){var a;if("object"==typeof t&&null!==t){for(a=0;a<o.length;a++)if(o[a]===t){var c=Object.getOwnPropertyDescriptor(s,i);return void(void 0!==c.get?c.configurable?(Object.defineProperty(s,i,{value:"[Circular]"}),n.push([s,i,t,c])):r.push([t,i]):(s[i]="[Circular]",n.push([s,i,t])))}if(o.push(t),Array.isArray(t))for(a=0;a<t.length;a++)e(t[a],a,o,t);else{var u=Object.keys(t);for(a=0;a<u.length;a++){var l=u[a];e(t[l],l,o,t)}}o.pop()}}(e,"",[],void 0),o=0===r.length?JSON.stringify(e,t,i):JSON.stringify(e,c(t),i);0!==n.length;){var s=n.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}return o}function o(e,t){return e<t?-1:e>t?1:0}function s(e,t,i){var o,s=a(e,"",[],void 0)||e;for(o=0===r.length?JSON.stringify(s,t,i):JSON.stringify(s,c(t),i);0!==n.length;){var u=n.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}return o}function a(e,t,i,s){var c;if("object"==typeof e&&null!==e){for(c=0;c<i.length;c++)if(i[c]===e){var u=Object.getOwnPropertyDescriptor(s,t);return void(void 0!==u.get?u.configurable?(Object.defineProperty(s,t,{value:"[Circular]"}),n.push([s,t,e,u])):r.push([e,t]):(s[t]="[Circular]",n.push([s,t,e])))}if("function"==typeof e.toJSON)return;if(i.push(e),Array.isArray(e))for(c=0;c<e.length;c++)a(e[c],c,i,e);else{var l={},f=Object.keys(e).sort(o);for(c=0;c<f.length;c++){var d=f[c];a(e[d],d,i,e),l[d]=e[d]}if(void 0===s)return l;n.push([s,t,e]),s[t]=l}i.pop()}}function c(e){return e=void 0!==e?e:function(e,t){return t},function(t,n){if(r.length>0)for(var i=0;i<r.length;i++){var o=r[i];if(o[1]===t&&o[0]===n){n="[Circular]",r.splice(i,1);break}}return e.call(this,t,n)}}},function(e,t){e.exports=require("stream")},,function(e,t,n){"use strict";e.exports={...n(30),...n(60),...n(63),...n(123),...n(125),...n(131),...n(11),...n(136),...n(138),...n(40),...n(18),...n(33)};const r=n(1);Object.getOwnPropertyDescriptor(r,"promises")&&Object.defineProperty(e.exports,"promises",{get:()=>r.promises})},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(4),o=["access","appendFile","chmod","chown","close","copyFile","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","link","lstat","mkdir","mkdtemp","open","opendir","readdir","readFile","readlink","realpath","rename","rm","rmdir","stat","symlink","truncate","unlink","utimes","writeFile"].filter(e=>"function"==typeof i[e]);Object.keys(i).forEach(e=>{"promises"!==e&&(t[e]=i[e])}),o.forEach(e=>{t[e]=r(i[e])}),t.exists=function(e,t){return"function"==typeof t?i.exists(e,t):new Promise(t=>i.exists(e,t))},t.read=function(e,t,n,r,o,s){return"function"==typeof s?i.read(e,t,n,r,o,s):new Promise((s,a)=>{i.read(e,t,n,r,o,(e,t,n)=>{if(e)return a(e);s({bytesRead:t,buffer:n})})})},t.write=function(e,t,...n){return"function"==typeof n[n.length-1]?i.write(e,t,...n):new Promise((r,o)=>{i.write(e,t,...n,(e,t,n)=>{if(e)return o(e);r({bytesWritten:t,buffer:n})})})},"function"==typeof i.writev&&(t.writev=function(e,t,...n){return"function"==typeof n[n.length-1]?i.writev(e,t,...n):new Promise((r,o)=>{i.writev(e,t,...n,(e,t,n)=>{if(e)return o(e);r({bytesWritten:t,buffers:n})})})}),"function"==typeof i.realpath.native&&(t.realpath.native=r(i.realpath.native))},function(e,t){e.exports=require("assert")},function(e,t,n){"use strict";const r=n(30),i=n(0),o=n(6),s=n(61)("10.5.0"),a=e=>s?r.stat(e,{bigint:!0}):r.stat(e),c=e=>s?r.statSync(e,{bigint:!0}):r.statSync(e);function u(e,t){return Promise.all([a(e),a(t).catch(e=>{if("ENOENT"===e.code)return null;throw e})]).then(([e,t])=>({srcStat:e,destStat:t}))}function l(e,t){if(t.ino&&t.dev&&t.ino===e.ino&&t.dev===e.dev){if(s||t.ino<Number.MAX_SAFE_INTEGER)return!0;if(t.size===e.size&&t.mode===e.mode&&t.nlink===e.nlink&&t.atimeMs===e.atimeMs&&t.mtimeMs===e.mtimeMs&&t.ctimeMs===e.ctimeMs&&t.birthtimeMs===e.birthtimeMs)return!0}return!1}function f(e,t){const n=i.resolve(e).split(i.sep).filter(e=>e),r=i.resolve(t).split(i.sep).filter(e=>e);return n.reduce((e,t,n)=>e&&r[n]===t,!0)}function d(e,t,n){return`Cannot ${n} '${e}' to a subdirectory of itself, '${t}'.`}e.exports={checkPaths:function(e,t,n,r){o.callbackify(u)(e,t,(i,o)=>{if(i)return r(i);const{srcStat:s,destStat:a}=o;return a&&l(s,a)?r(new Error("Source and destination must not be the same.")):s.isDirectory()&&f(e,t)?r(new Error(d(e,t,n))):r(null,{srcStat:s,destStat:a})})},checkPathsSync:function(e,t,n){const{srcStat:r,destStat:i}=function(e,t){let n;const r=c(e);try{n=c(t)}catch(e){if("ENOENT"===e.code)return{srcStat:r,destStat:null};throw e}return{srcStat:r,destStat:n}}(e,t);if(i&&l(r,i))throw new Error("Source and destination must not be the same.");if(r.isDirectory()&&f(e,t))throw new Error(d(e,t,n));return{srcStat:r,destStat:i}},checkParentPaths:function e(t,n,o,a,c){const u=i.resolve(i.dirname(t)),f=i.resolve(i.dirname(o));if(f===u||f===i.parse(f).root)return c();const h=(r,i)=>r?"ENOENT"===r.code?c():c(r):l(n,i)?c(new Error(d(t,o,a))):e(t,n,f,a,c);s?r.stat(f,{bigint:!0},h):r.stat(f,h)},checkParentPathsSync:function e(t,n,r,o){const s=i.resolve(i.dirname(t)),a=i.resolve(i.dirname(r));if(a===s||a===i.parse(a).root)return;let u;try{u=c(a)}catch(e){if("ENOENT"===e.code)return;throw e}if(l(n,u))throw new Error(d(t,r,o));return e(t,n,a,o)},isSrcSubdir:f}},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(124);e.exports={remove:r(i),removeSync:i.sync}},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.create=function(e){return{dispose:e}}}(t.Disposable||(t.Disposable={})),function(e){const t={dispose(){}};e.None=function(){return t}}(t.Event||(t.Event={}));class r{add(e,t=null,n){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(e),this._contexts.push(t),Array.isArray(n)&&n.push({dispose:()=>this.remove(e,t)})}remove(e,t=null){if(this._callbacks){for(var n=!1,r=0,i=this._callbacks.length;r<i;r++)if(this._callbacks[r]===e){if(this._contexts[r]===t)return this._callbacks.splice(r,1),void this._contexts.splice(r,1);n=!0}if(n)throw new Error("When adding a listener with a context, you should remove it with the same context")}}invoke(...e){if(!this._callbacks)return[];for(var t=[],n=this._callbacks.slice(0),r=this._contexts.slice(0),i=0,o=n.length;i<o;i++)try{t.push(n[i].apply(r[i],e))}catch(e){console.error(e)}return t}isEmpty(){return!this._callbacks||0===this._callbacks.length}dispose(){this._callbacks=void 0,this._contexts=void 0}}class i{constructor(e){this._options=e}get event(){return this._event||(this._event=(e,t,n)=>{let o;return this._callbacks||(this._callbacks=new r),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(e,t),o={dispose:()=>{this._callbacks.remove(e,t),o.dispose=i._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this)}},Array.isArray(n)&&n.push(o),o}),this._event}fire(e){this._callbacks&&this._callbacks.invoke.call(this._callbacks,e)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}t.Emitter=i,i._noop=function(){}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getFilesPathsFromFolder=t.createFolderIfNotExist=t.fileWrite=t.writeFileIfNotExist=t.OS_TEMP_FOLDER=void 0;const i=n(1),o=n(3),{readdir:s}=n(1).promises,a=n(0),c=n(19);t.OS_TEMP_FOLDER=o.tmpdir(),t.writeFileIfNotExist=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield i.promises.stat(e).then(()=>!0).catch(()=>!1)))return yield i.promises.writeFile(e,t).then(()=>!0).catch(()=>!1)}catch(e){console.log(e)}})),t.fileWrite=(e,t)=>r(void 0,void 0,void 0,(function*(){return yield i.promises.writeFile(e,t).then(()=>!0).catch(()=>!1)})),t.createFolderIfNotExist=e=>r(void 0,void 0,void 0,(function*(){if(!(yield i.promises.stat(e).then(()=>!0).catch(()=>!1)))return yield i.promises.mkdir(e).then(()=>!0).catch(()=>!1)})),t.getFilesPathsFromFolder=function e(t){return r(this,void 0,void 0,(function*(){try{const n=yield s(t,{withFileTypes:!0}),r=yield Promise.all(n.map(n=>{const r=a.resolve(t,n.name);return n.isDirectory()?e(r):r}));return Array.prototype.concat(...r)}catch(e){c.default.error("Try get files paths from workspace")}}))}},function(e,t,n){"use strict";n.r(t),n.d(t,"URI",(function(){return p})),n.d(t,"uriToFsPath",(function(){return _}));var r,i,o,s=(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});if("object"==typeof process)o="win32"===process.platform;else if("object"==typeof navigator){var a=navigator.userAgent;o=a.indexOf("Windows")>=0}var c=/^\w[\w\d+.-]*$/,u=/^\//,l=/^\/\//,f="",d="/",h=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,p=function(){function e(e,t,n,r,i,o){void 0===o&&(o=!1),"object"==typeof e?(this.scheme=e.scheme||f,this.authority=e.authority||f,this.path=e.path||f,this.query=e.query||f,this.fragment=e.fragment||f):(this.scheme=function(e,t){return e||t?e:"file"}(e,o),this.authority=t||f,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==d&&(t=d+t):t=d}return t}(this.scheme,n||f),this.query=r||f,this.fragment=i||f,function(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!c.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!u.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(l.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}(this,o))}return e.isUri=function(t){return t instanceof e||!!t&&"string"==typeof t.authority&&"string"==typeof t.fragment&&"string"==typeof t.path&&"string"==typeof t.query&&"string"==typeof t.scheme&&"function"==typeof t.fsPath&&"function"==typeof t.with&&"function"==typeof t.toString},Object.defineProperty(e.prototype,"fsPath",{get:function(){return _(this,!1)},enumerable:!0,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,n=e.authority,r=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=f),void 0===n?n=this.authority:null===n&&(n=f),void 0===r?r=this.path:null===r&&(r=f),void 0===i?i=this.query:null===i&&(i=f),void 0===o?o=this.fragment:null===o&&(o=f),t===this.scheme&&n===this.authority&&r===this.path&&i===this.query&&o===this.fragment?this:new g(t,n,r,i,o)},e.parse=function(e,t){void 0===t&&(t=!1);var n=h.exec(e);return n?new g(n[2]||f,x(n[4]||f),x(n[5]||f),x(n[7]||f),x(n[9]||f),t):new g(f,f,f,f,f)},e.file=function(e){var t=f;if(o&&(e=e.replace(/\\/g,d)),e[0]===d&&e[1]===d){var n=e.indexOf(d,2);-1===n?(t=e.substring(2),e=d):(t=e.substring(2,n),e=e.substring(n)||d)}return new g("file",t,e,f,f)},e.from=function(e){return new g(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),w(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var n=new g(t);return n._formatted=t.external,n._fsPath=t._sep===m?t.fsPath:null,n}return t},e}(),m=o?1:void 0,g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return s(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=_(this,!1)),this._fsPath},enumerable:!0,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?w(this,!0):(this._formatted||(this._formatted=w(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=m),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(p),y=((i={})[58]="%3A",i[47]="%2F",i[63]="%3F",i[35]="%23",i[91]="%5B",i[93]="%5D",i[64]="%40",i[33]="%21",i[36]="%24",i[38]="%26",i[39]="%27",i[40]="%28",i[41]="%29",i[42]="%2A",i[43]="%2B",i[44]="%2C",i[59]="%3B",i[61]="%3D",i[32]="%20",i);function v(e,t){for(var n=void 0,r=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),void 0!==n&&(n+=e.charAt(i));else{void 0===n&&(n=e.substr(0,i));var s=y[o];void 0!==s?(-1!==r&&(n+=encodeURIComponent(e.substring(r,i)),r=-1),n+=s):-1===r&&(r=i)}}return-1!==r&&(n+=encodeURIComponent(e.substring(r))),void 0!==n?n:e}function b(e){for(var t=void 0,n=0;n<e.length;n++){var r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=y[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function _(e,t){var n;return n=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?t?e.path.substr(1):e.path[1].toLowerCase()+e.path.substr(2):e.path,o&&(n=n.replace(/\//g,"\\")),n}function w(e,t){var n=t?b:v,r="",i=e.scheme,o=e.authority,s=e.path,a=e.query,c=e.fragment;if(i&&(r+=i,r+=":"),(o||"file"===i)&&(r+=d,r+=d),o){var u=o.indexOf("@");if(-1!==u){var l=o.substr(0,u);o=o.substr(u+1),-1===(u=l.indexOf(":"))?r+=n(l,!1):(r+=n(l.substr(0,u),!1),r+=":",r+=n(l.substr(u+1),!1)),r+="@"}-1===(u=(o=o.toLowerCase()).indexOf(":"))?r+=n(o,!1):(r+=n(o.substr(0,u),!1),r+=o.substr(u))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2))(f=s.charCodeAt(1))>=65&&f<=90&&(s="/"+String.fromCharCode(f+32)+":"+s.substr(3));else if(s.length>=2&&58===s.charCodeAt(1)){var f;(f=s.charCodeAt(0))>=65&&f<=90&&(s=String.fromCharCode(f+32)+":"+s.substr(2))}r+=n(s,!0)}return a&&(r+="?",r+=n(a,!1)),c&&(r+="#",r+=t?c:v(c,!1)),r}var S=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function x(e){return e.match(S)?e.replace(S,(function(e){return function e(t){try{return decodeURIComponent(t)}catch(n){return t.length>3?t.substr(0,3)+e(t.substr(3)):t}}(e)})):e}},,function(e,t){e.exports={stringify:function(e,{EOL:t="\n",finalEOL:n=!0,replacer:r=null,spaces:i}={}){const o=n?t:"";return JSON.stringify(e,r,i).replace(/\n/g,t)+o},stripBom:function(e){return Buffer.isBuffer(e)&&(e=e.toString("utf8")),e.replace(/^\uFEFF/,"")}}},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(4),o=n(0),s=n(11),a=n(18).pathExists;e.exports={outputFile:r((function(e,t,n,r){"function"==typeof n&&(r=n,n="utf8");const c=o.dirname(e);a(c,(o,a)=>o?r(o):a?i.writeFile(e,t,n,r):void s.mkdirs(c,o=>{if(o)return r(o);i.writeFile(e,t,n,r)}))})),outputFileSync:function(e,...t){const n=o.dirname(e);if(i.existsSync(n))return i.writeFileSync(e,...t);s.mkdirsSync(n),i.writeFileSync(e,...t)}}},function(e,t){e.exports=require("child_process")},function(e,t,n){"use strict";e.exports=/[^.[\]]+|\[((?:.)*?)\]/g},function(e,t,n){"use strict";e.exports=function(e){return e}},function(e,t,n){"use strict";const r=n(157),{mapHttpRequest:i,mapHttpResponse:o}=n(64),s=n(67),a=n(158),{lsCacheSym:c,chindingsSym:u,parsedChindingsSym:l,writeSym:f,serializersSym:d,formatOptsSym:h,endSym:p,stringifiersSym:m,stringifySym:g,wildcardFirstSym:y,needsMetadataGsym:v,redactFmtSym:b,streamSym:_,nestedKeySym:w,formattersSym:S,messageKeySym:x}=n(21);function k(){}function E(e){let t="",n=0,r=!1,i=255;const o=e.length;if(o>100)return JSON.stringify(e);for(var s=0;s<o&&i>=32;s++)i=e.charCodeAt(s),34!==i&&92!==i||(t+=e.slice(n,s)+"\\",n=s,r=!0);return r?t+=e.slice(n):t=e,i<32?JSON.stringify(e):'"'+t+'"'}function T(e,t,r,i){if(t&&"function"==typeof t)return R((t=t.bind(i))(e),r,e);try{const t=n(159);return t.asMetaWrapper=R,R(t(e),r,e)}catch(e){if(e.message.startsWith("Cannot find module 'pino-pretty'"))throw Error("Missing `pino-pretty` module: `pino-pretty` must be installed separately");throw e}}function R(e,t,n){n=Object.assign({suppressFlushSyncWarning:!1},n);let r=!1;return{[v]:!0,lastLevel:0,lastMsg:null,lastObj:null,lastLogger:null,flushSync(){n.suppressFlushSyncWarning||r||(r=!0,O(t,this),t.write(e(Object.assign({level:40,msg:"pino.final with prettyPrint does not support flushing",time:Date.now()},this.chindings()))))},chindings(){const e=this.lastLogger;let t=null;return e?(e.hasOwnProperty(l)?t=e[l]:(t=JSON.parse("{"+e[u].substr(1)+"}"),e[l]=t),t):null},write(n){const r=this.lastLogger,i=this.chindings();let o=this.lastTime;o=o.match(/^\d+/)?parseInt(o):o.slice(1,-1);const s=this.lastObj,a=this.lastMsg,c=r[S],u=c.log?c.log(s):s,l=r[x];a&&u&&!u.hasOwnProperty(l)&&(u[l]=a);const f=Object.assign({level:this.lastLevel,time:o},u,null),h=r[d],p=Object.keys(h);for(var g=0;g<p.length;g++){const e=p[g];void 0!==f[e]&&(f[e]=h[e](f[e]))}for(const e in i)f.hasOwnProperty(e)||(f[e]=i[e]);const y=r[m][b],v=e("function"==typeof y?y(f):f);void 0!==v&&(O(t,this),t.write(v))}}}function P(e){const t=new s(e);return t.on("error",(function e(n){if("EPIPE"===n.code)return t.write=k,t.end=k,t.flushSync=k,void(t.destroy=k);t.removeListener("error",e),t.emit("error",n)})),t}function O(e,t){!0===e[v]&&(e.lastLevel=t.lastLevel,e.lastMsg=t.lastMsg,e.lastObj=t.lastObj,e.lastTime=t.lastTime,e.lastLogger=t.lastLogger)}e.exports={noop:k,buildSafeSonicBoom:P,getPrettyStream:T,asChindings:function(e,t){let n,r=e[u];const i=e[g],o=e[m],s=o[y],a=e[d];t=(0,e[S].bindings)(t);for(const e in t)if(n=t[e],!0===("level"!==e&&"serializers"!==e&&"formatters"!==e&&"customLevels"!==e&&t.hasOwnProperty(e)&&void 0!==n)){if(n=a[e]?a[e](n):n,n=(o[e]||s||i)(n),void 0===n)continue;r+=',"'+e+'":'+n}return r},asJson:function(e,t,n,r){const i=this[g],o=this[m],s=this[p],a=this[u],l=this[d],f=this[S],h=this[x];let v,b=this[c][n]+r;b+=a;const _=void 0===e.hasOwnProperty;f.log&&(e=f.log(e)),void 0!==t&&(e[h]=t);const w=o[y];for(const t in e)if(v=e[t],(_||e.hasOwnProperty(t))&&void 0!==v){v=l[t]?l[t](v):v;const e=o[t]||w;switch(typeof v){case"undefined":case"function":continue;case"number":!1===Number.isFinite(v)&&(v=null);case"boolean":e&&(v=e(v));break;case"string":v=(e||E)(v);break;default:v=(e||i)(v)}if(void 0===v)continue;b+=',"'+t+'":'+v}return b+s},genLog:function(e,t){return t?function(...r){t.call(this,r,n,e)}:n;function n(t,...n){if("object"==typeof t){let s,a=t;null!==t&&(t.method&&t.headers&&t.socket?t=i(t):"function"==typeof t.setHeader&&(t=o(t))),this[w]&&(t={[this[w]]:t}),null===a&&0===n.length?s=[null]:(a=n.shift(),s=n),this[f](t,r(a,s,this[h]),e)}else this[f](null,r(t,n,this[h]),e)}},createArgsNormalizer:function(e){return function(t,n={},r){if("string"==typeof n?(r=P({dest:n,sync:!0}),n={}):"string"==typeof r?r=P({dest:r,sync:!0}):(n instanceof s||n.writable||n._writableState)&&(r=n,n=null),"extreme"in(n=Object.assign({},e,n)))throw Error("The extreme option has been removed, use pino.destination({ sync: false }) instead");if("onTerminated"in n)throw Error("The onTerminated option has been removed, use pino.final instead");"changeLevelName"in n&&(process.emitWarning("The changeLevelName option is deprecated and will be removed in v7. Use levelKey instead.",{code:"changeLevelName_deprecation"}),n.levelKey=n.changeLevelName,delete n.changeLevelName);const{enabled:i,prettyPrint:o,prettifier:a,messageKey:c}=n;return!1===i&&(n.level="silent"),(r=r||process.stdout)===process.stdout&&r.fd>=0&&!function(e){return e.write!==e.constructor.prototype.write}(r)&&(r=P({fd:r.fd,sync:!0})),o&&(r=T(Object.assign({messageKey:c},o),a,r,t)),{opts:n,stream:r}}},final:function(e,t){if(void 0===e||"function"!=typeof e.child)throw Error("expected a pino logger instance");const n=void 0!==t;if(n&&"function"!=typeof t)throw Error("if supplied, the handler parameter should be a function");const r=e[_];if("function"!=typeof r.flushSync)throw Error("final requires a stream that has a flushSync method, such as pino.destination");const i=new Proxy(e,{get:(e,t)=>t in e.levels.values?(...n)=>{e[t](...n),r.flushSync()}:e[t]});return n?(e=null,...n)=>{try{r.flushSync()}catch(e){}return t(e,i,...n)}:i},stringify:function(e){try{return JSON.stringify(e)}catch(t){return a(e)}},buildFormatters:function(e,t,n){return{level:e,bindings:t,log:n}}}},function(e,t,n){"use strict";e.exports={DATE_FORMAT:"yyyy-mm-dd HH:MM:ss.l o",ERROR_LIKE_KEYS:["err","error"],MESSAGE_KEY:"msg",LEVEL_KEY:"level",LEVEL_LABEL:"levelLabel",TIMESTAMP_KEY:"time",LEVELS:{default:"USERLVL",60:"FATAL",50:"ERROR",40:"WARN",30:"INFO",20:"DEBUG",10:"TRACE"},LEVEL_NAMES:{fatal:60,error:50,warn:40,info:30,debug:20,trace:10},LOGGER_KEYS:["pid","hostname","name","level","time","timestamp","caller"]}},function(e,t,n){var r;r=function(){var e={},t="undefined"!=typeof process&&"win32"===process.platform?"\r\n":"\n",n=/\r\n|\r|\n/g;function r(e){function t(t){return t.replace(n,e)}return t.toString=function(){return e},t}return e.lf=r("\n"),e.cr=r("\r"),e.crlf=r("\r\n"),e.auto=r(t),e.before=function(e){return t+e},e.after=function(e){return e+t},e.split=function(e){return e.split(n)},e},e.exports?e.exports=r():this.eol=r()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(35),i=n(22);let o=8192,s=Buffer.from("\r","ascii")[0],a=Buffer.from("\n","ascii")[0];class c{constructor(e="utf8"){this.encoding=e,this.index=0,this.buffer=Buffer.allocUnsafe(o)}append(e){var t=e;if("string"==typeof e){var n=e,r=Buffer.byteLength(n,this.encoding);(t=Buffer.allocUnsafe(r)).write(n,0,r,this.encoding)}if(this.buffer.length-this.index>=t.length)t.copy(this.buffer,this.index,0,t.length);else{var i=(Math.ceil((this.index+t.length)/o)+1)*o;0===this.index?(this.buffer=Buffer.allocUnsafe(i),t.copy(this.buffer,0,0,t.length)):this.buffer=Buffer.concat([this.buffer.slice(0,this.index),t],i)}this.index+=t.length}tryReadHeaders(){let e,t=0;for(;t+3<this.index&&(this.buffer[t]!==s||this.buffer[t+1]!==a||this.buffer[t+2]!==s||this.buffer[t+3]!==a);)t++;if(t+3>=this.index)return e;e=Object.create(null),this.buffer.toString("ascii",0,t).split("\r\n").forEach(t=>{let n=t.indexOf(":");if(-1===n)throw new Error("Message header must separate key and value using :");let r=t.substr(0,n),i=t.substr(n+1).trim();e[r]=i});let n=t+4;return this.buffer=this.buffer.slice(n),this.index=this.index-n,e}tryReadContent(e){if(this.index<e)return null;let t=this.buffer.toString(this.encoding,0,e),n=e;return this.buffer.copy(this.buffer,0,n),this.index=this.index-n,t}get numberOfBytes(){return this.index}}!function(e){e.is=function(e){let t=e;return t&&i.func(t.listen)&&i.func(t.dispose)&&i.func(t.onError)&&i.func(t.onClose)&&i.func(t.onPartialMessage)}}(t.MessageReader||(t.MessageReader={}));class u{constructor(){this.errorEmitter=new r.Emitter,this.closeEmitter=new r.Emitter,this.partialMessageEmitter=new r.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e){this.errorEmitter.fire(this.asError(e))}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}get onPartialMessage(){return this.partialMessageEmitter.event}firePartialMessage(e){this.partialMessageEmitter.fire(e)}asError(e){return e instanceof Error?e:new Error("Reader received error. Reason: "+(i.string(e.message)?e.message:"unknown"))}}t.AbstractMessageReader=u;class l extends u{constructor(e,t="utf8"){super(),this.readable=e,this.buffer=new c(t),this._partialMessageTimeout=1e4}set partialMessageTimeout(e){this._partialMessageTimeout=e}get partialMessageTimeout(){return this._partialMessageTimeout}listen(e){this.nextMessageLength=-1,this.messageToken=0,this.partialMessageTimer=void 0,this.callback=e,this.readable.on("data",e=>{this.onData(e)}),this.readable.on("error",e=>this.fireError(e)),this.readable.on("close",()=>this.fireClose())}onData(e){for(this.buffer.append(e);;){if(-1===this.nextMessageLength){let e=this.buffer.tryReadHeaders();if(!e)return;let t=e["Content-Length"];if(!t)throw new Error("Header must provide a Content-Length property.");let n=parseInt(t);if(isNaN(n))throw new Error("Content-Length value must be a number.");this.nextMessageLength=n}var t=this.buffer.tryReadContent(this.nextMessageLength);if(null===t)return void this.setPartialMessageTimer();this.clearPartialMessageTimer(),this.nextMessageLength=-1,this.messageToken++;var n=JSON.parse(t);this.callback(n)}}clearPartialMessageTimer(){this.partialMessageTimer&&(clearTimeout(this.partialMessageTimer),this.partialMessageTimer=void 0)}setPartialMessageTimer(){this.clearPartialMessageTimer(),this._partialMessageTimeout<=0||(this.partialMessageTimer=setTimeout((e,t)=>{this.partialMessageTimer=void 0,e===this.messageToken&&(this.firePartialMessage({messageToken:e,waitingTime:t}),this.setPartialMessageTimer())},this._partialMessageTimeout,this.messageToken,this._partialMessageTimeout))}}t.StreamMessageReader=l,t.IPCMessageReader=class extends u{constructor(e){super(),this.process=e;let t=this.process;t.on("error",e=>this.fireError(e)),t.on("close",()=>this.fireClose())}listen(e){this.process.on("message",e)}},t.SocketMessageReader=class extends l{constructor(e,t="utf-8"){super(e,t)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(35),i=n(22);let o="Content-Length: ",s="\r\n";!function(e){e.is=function(e){let t=e;return t&&i.func(t.dispose)&&i.func(t.onClose)&&i.func(t.onError)&&i.func(t.write)}}(t.MessageWriter||(t.MessageWriter={}));class a{constructor(){this.errorEmitter=new r.Emitter,this.closeEmitter=new r.Emitter}dispose(){this.errorEmitter.dispose(),this.closeEmitter.dispose()}get onError(){return this.errorEmitter.event}fireError(e,t,n){this.errorEmitter.fire([this.asError(e),t,n])}get onClose(){return this.closeEmitter.event}fireClose(){this.closeEmitter.fire(void 0)}asError(e){return e instanceof Error?e:new Error("Writer received error. Reason: "+(i.string(e.message)?e.message:"unknown"))}}t.AbstractMessageWriter=a,t.StreamMessageWriter=class extends a{constructor(e,t="utf8"){super(),this.writable=e,this.encoding=t,this.errorCount=0,this.writable.on("error",e=>this.fireError(e)),this.writable.on("close",()=>this.fireClose())}write(e){let t=JSON.stringify(e),n=Buffer.byteLength(t,this.encoding),r=[o,n.toString(),s,s];try{this.writable.write(r.join(""),"ascii"),this.writable.write(t,this.encoding),this.errorCount=0}catch(t){this.errorCount++,this.fireError(t,e,this.errorCount)}}},t.IPCMessageWriter=class extends a{constructor(e){super(),this.process=e,this.errorCount=0,this.queue=[],this.sending=!1;let t=this.process;t.on("error",e=>this.fireError(e)),t.on("close",()=>this.fireClose)}write(e){this.sending||0!==this.queue.length?this.queue.push(e):this.doWriteMessage(e)}doWriteMessage(e){try{this.process.send&&(this.sending=!0,this.process.send(e,void 0,void 0,t=>{this.sending=!1,t?(this.errorCount++,this.fireError(t,e,this.errorCount)):this.errorCount=0,this.queue.length>0&&this.doWriteMessage(this.queue.shift())}))}catch(t){this.errorCount++,this.fireError(t,e,this.errorCount)}}},t.SocketMessageWriter=class extends a{constructor(e,t="utf8"){super(),this.socket=e,this.queue=[],this.sending=!1,this.encoding=t,this.errorCount=0,this.socket.on("error",e=>this.fireError(e)),this.socket.on("close",()=>this.fireClose())}dispose(){super.dispose(),this.socket.destroy()}write(e){this.sending||0!==this.queue.length?this.queue.push(e):this.doWriteMessage(e)}doWriteMessage(e){let t=JSON.stringify(e),n=Buffer.byteLength(t,this.encoding),r=[o,n.toString(),s,s];try{this.sending=!0,this.socket.write(r.join(""),"ascii",n=>{n&&this.handleError(n,e);try{this.socket.write(t,this.encoding,t=>{this.sending=!1,t?this.handleError(t,e):this.errorCount=0,this.queue.length>0&&this.doWriteMessage(this.queue.shift())})}catch(n){this.handleError(n,e)}})}catch(t){this.handleError(t,e)}}handleError(e,t){this.errorCount++,this.fireError(e,t,this.errorCount)}}},,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RegexPatterns=void 0,t.RegexPatterns={comments:/^( *\'\/\* *?\r\n?)( *(\'\r\n)?\'.*\r\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)/gim,comments_first:/^( *\'\/\* *?\r?\n?)( *(\'\r\n)?\'.*\r?\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)/im,comment_class:/(^( *\'\/\* *?\r\n?)( *(\'\r\n)?\'.*\r\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)(\n?\r?\s)+?)?( *Public *Class *.*\)\s)/gim,class_name:/^( *?Public +Class *.*\)\n?\r?\s?)/gim,class_name_label:/(?<= *public +Class +)(.*?)(?= *\()/gim,class_prop_name:/^( *?Public +Property *.*\n?\r?\s?)/gim,class_prop_label:/(?<= *public +Property +)(.*?)(?= *as)/gim,class_event_name:/^( *?Public +Event *.*\n?\r?\s?)/gim,class_event_label:/(?<= *public +Event +)(.*?)(?= *\()/gim,class_method_name:/^( *?Public)?( *(Sub|function) *.*\n?\r?\s?)/gim,class_method_label:/(?<= *(public)? *(sub|function) +)(.*?)(?= *\()/gim,attribute_bv_name:/(?<=Attribute VB_name = \")(.*?)(?=")/im,comments_class_end:/(^( *\'\/\* *?\r?\n?)( *(\'\r?\n)?\'.*\r?\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)(\n?\r?\s)+?)?^( *Public *Class *.*\s)^( *.*\n?\r?)+?^( *end +class)/gim,comments_sub:/^(.*\'\/\*.*\s?\r?\n?)(\s?\r?\n?\'.*\s?\r?\n?)*(\s?\n?\t?.*sub\s.*)(\))/gim,comments_sub_end:/(^( *\'\/\* *?\r?\n?)( *(\'\r\n)?\'.*\r?\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)(\n?\r?\s)+?)?^( *public)? *(sub|function)( *.*\s)( *.*\n?\r?)+?^( *end +)(sub|function)/gim,comments_public_property:/(^( *\'\/\* *?\r\n?)( *(\'\r\n)?\'.*\r\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)(\n?\r?\s)+?)?( *Public +Property +.*)/gim,comments_public_event:/(^( *\'\/\* *?\r\n?)( *(\'\r\n)?\'.*\r\n?)+?( *?\'\*\/ *\n?\r?\s?\t?)(\n?\r?\s)+?)?( *Public +Event +.*)/gim,param_comments:/^(.*\'\/\*.*\s?\r?\n?)(\s?\r?\n?\'.*\s?\r?\n?)*(\s?\n?\t?.*dim\s.*)(as\s.*)/gim,function:/(^.*)(?=sub|function)(.*?)(\))/gim,functionEnd:/end\s*(sub|function)/gim,macroName:/(?<=sub |function )(.*?)(?=\s*\()/gi,macroParams:/(?<=\()(.*?)(?=\s*\))/gi,namespace:/(?<=\' *?namespace *?=).*/gim,logMessageType:/(?<=\- )(.*?)(?=\s*\:)/gi,new_lines_crlf:/^\s*[\r\n]/g}},,,function(e,t){e.exports=require("url")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9);t.Event=r.Event;const i=n(480),o=n(481),s=n(482),a=n(297),c=n(298);!function(e){for(var n in e)t.hasOwnProperty(n)||(t[n]=e[n])}(n(9));const u=n(483);!function(e){e.uriToFilePath=u.uriToFilePath,e.resolveGlobalNodePath=u.resolveGlobalNodePath,e.resolveGlobalYarnPath=u.resolveGlobalYarnPath,e.resolve=u.resolve,e.resolveModulePath=u.resolveModulePath}(t.Files||(t.Files={}));let l,f=!1;function d(e){if(null!==e)return e}!function(){const e="--clientProcessId";function t(e){try{let t=parseInt(e);isNaN(t)||(l=setInterval(()=>{try{process.kill(t,0)}catch(e){process.exit(f?0:1)}},3e3))}catch(e){}}for(let n=2;n<process.argv.length;n++){let r=process.argv[n];if(r===e&&n+1<process.argv.length)return void t(process.argv[n+1]);{let n=r.split("=");n[0]===e&&t(n[1])}}}(),t.TextDocuments=class{constructor(e){this._documents=Object.create(null),this._configuration=e,this._onDidChangeContent=new r.Emitter,this._onDidOpen=new r.Emitter,this._onDidClose=new r.Emitter,this._onDidSave=new r.Emitter,this._onWillSave=new r.Emitter}get onDidChangeContent(){return this._onDidChangeContent.event}get onDidOpen(){return this._onDidOpen.event}get onWillSave(){return this._onWillSave.event}onWillSaveWaitUntil(e){this._willSaveWaitUntil=e}get onDidSave(){return this._onDidSave.event}get onDidClose(){return this._onDidClose.event}get(e){return this._documents[e]}all(){return Object.keys(this._documents).map(e=>this._documents[e])}keys(){return Object.keys(this._documents)}listen(e){e.__textDocumentSync=r.TextDocumentSyncKind.Full,e.onDidOpenTextDocument(e=>{let t=e.textDocument,n=this._configuration.create(t.uri,t.languageId,t.version,t.text);this._documents[t.uri]=n;let r=Object.freeze({document:n});this._onDidOpen.fire(r),this._onDidChangeContent.fire(r)}),e.onDidChangeTextDocument(e=>{let t=e.textDocument,n=e.contentChanges;if(0===n.length)return;let r=this._documents[t.uri];const{version:i}=t;if(null==i)throw new Error(`Received document change event for ${t.uri} without valid version identifier`);r=this._configuration.update(r,n,i),this._documents[t.uri]=r,this._onDidChangeContent.fire(Object.freeze({document:r}))}),e.onDidCloseTextDocument(e=>{let t=this._documents[e.textDocument.uri];t&&(delete this._documents[e.textDocument.uri],this._onDidClose.fire(Object.freeze({document:t})))}),e.onWillSaveTextDocument(e=>{let t=this._documents[e.textDocument.uri];t&&this._onWillSave.fire(Object.freeze({document:t,reason:e.reason}))}),e.onWillSaveTextDocumentWaitUntil((e,t)=>{let n=this._documents[e.textDocument.uri];return n&&this._willSaveWaitUntil?this._willSaveWaitUntil(Object.freeze({document:n,reason:e.reason}),t):[]}),e.onDidSaveTextDocument(e=>{let t=this._documents[e.textDocument.uri];t&&this._onDidSave.fire(Object.freeze({document:t}))})}},t.ErrorMessageTracker=class{constructor(){this._messages=Object.create(null)}add(e){let t=this._messages[e];t||(t=0),t++,this._messages[e]=t}sendErrors(e){Object.keys(this._messages).forEach(t=>{e.window.showErrorMessage(t)})}};class h{constructor(){}rawAttach(e){this._rawConnection=e}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}fillServerCapabilities(e){}initialize(e){}error(e){this.send(r.MessageType.Error,e)}warn(e){this.send(r.MessageType.Warning,e)}info(e){this.send(r.MessageType.Info,e)}log(e){this.send(r.MessageType.Log,e)}send(e,t){this._rawConnection&&this._rawConnection.sendNotification(r.LogMessageNotification.type,{type:e,message:t})}}const p=s.ProgressFeature(class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}showErrorMessage(e,...t){let n={type:r.MessageType.Error,message:e,actions:t};return this._connection.sendRequest(r.ShowMessageRequest.type,n).then(d)}showWarningMessage(e,...t){let n={type:r.MessageType.Warning,message:e,actions:t};return this._connection.sendRequest(r.ShowMessageRequest.type,n).then(d)}showInformationMessage(e,...t){let n={type:r.MessageType.Info,message:e,actions:t};return this._connection.sendRequest(r.ShowMessageRequest.type,n).then(d)}});!function(e){e.create=function(){return new m}}(t.BulkRegistration||(t.BulkRegistration={}));class m{constructor(){this._registrations=[],this._registered=new Set}add(e,t){const n=a.string(e)?e:e.method;if(this._registered.has(n))throw new Error(n+" is already added to this registration");const r=c.generateUuid();this._registrations.push({id:r,method:n,registerOptions:t||{}}),this._registered.add(n)}asRegistrationParams(){return{registrations:this._registrations}}}!function(e){e.create=function(){return new g(void 0,[])}}(t.BulkUnregistration||(t.BulkUnregistration={}));class g{constructor(e,t){this._connection=e,this._unregistrations=new Map,t.forEach(e=>{this._unregistrations.set(e.method,e)})}get isAttached(){return!!this._connection}attach(e){this._connection=e}add(e){this._unregistrations.set(e.method,e)}dispose(){let e=[];for(let t of this._unregistrations.values())e.push(t);let t={unregisterations:e};this._connection.sendRequest(r.UnregistrationRequest.type,t).then(void 0,e=>{this._connection.console.info("Bulk unregistration failed.")})}disposeSingle(e){const t=a.string(e)?e:e.method,n=this._unregistrations.get(t);if(!n)return!1;let i={unregisterations:[n]};return this._connection.sendRequest(r.UnregistrationRequest.type,i).then(()=>{this._unregistrations.delete(t)},e=>{this._connection.console.info(`Unregistering request handler for ${n.id} failed.`)}),!0}}class y{attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}register(e,t,n){return e instanceof m?this.registerMany(e):e instanceof g?this.registerSingle1(e,t,n):this.registerSingle2(e,t)}registerSingle1(e,t,n){const i=a.string(t)?t:t.method,o=c.generateUuid();let s={registrations:[{id:o,method:i,registerOptions:n||{}}]};return e.isAttached||e.attach(this._connection),this._connection.sendRequest(r.RegistrationRequest.type,s).then(t=>(e.add({id:o,method:i}),e),e=>(this.connection.console.info(`Registering request handler for ${i} failed.`),Promise.reject(e)))}registerSingle2(e,t){const n=a.string(e)?e:e.method,i=c.generateUuid();let o={registrations:[{id:i,method:n,registerOptions:t||{}}]};return this._connection.sendRequest(r.RegistrationRequest.type,o).then(e=>r.Disposable.create(()=>{this.unregisterSingle(i,n)}),e=>(this.connection.console.info(`Registering request handler for ${n} failed.`),Promise.reject(e)))}unregisterSingle(e,t){let n={unregisterations:[{id:e,method:t}]};return this._connection.sendRequest(r.UnregistrationRequest.type,n).then(void 0,t=>{this.connection.console.info(`Unregistering request handler for ${e} failed.`)})}registerMany(e){let t=e.asRegistrationParams();return this._connection.sendRequest(r.RegistrationRequest.type,t).then(()=>new g(this._connection,t.registrations.map(e=>({id:e.id,method:e.method}))),e=>(this.connection.console.info("Bulk registration failed."),Promise.reject(e)))}}const v=o.WorkspaceFoldersFeature(i.ConfigurationFeature(class{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}applyEdit(e){let t=(n=e)&&n.edit?e:{edit:e};var n;return this._connection.sendRequest(r.ApplyWorkspaceEditRequest.type,t)}}));class b{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}logEvent(e){this._connection.sendNotification(r.TelemetryEventNotification.type,e)}}class _{constructor(){this._trace=r.Trace.Off}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}set trace(e){this._trace=e}log(e,t){this._trace!==r.Trace.Off&&this._connection.sendNotification(r.LogTraceNotification.type,{message:e,verbose:this._trace===r.Trace.Verbose?t:void 0})}}class w{constructor(){}attach(e){this._connection=e}get connection(){if(!this._connection)throw new Error("Remote is not attached to a connection yet.");return this._connection}initialize(e){}fillServerCapabilities(e){}attachWorkDoneProgress(e){return s.attachWorkDone(this.connection,e)}attachPartialResultProgress(e,t){return s.attachPartialResult(this.connection,t)}}function S(e,t){return function(n){return t(e(n))}}function x(e,t){return function(n){return t(e(n))}}function k(e,t){return function(n){return t(e(n))}}function E(e,t){return function(n){return t(e(n))}}function T(e,t){return function(n){return t(e(n))}}function R(e,t){return function(n){return t(e(n))}}function P(e,t){return function(n){return t(e(n))}}t.LanguagesImpl=w,t.combineConsoleFeatures=S,t.combineTelemetryFeatures=x,t.combineTracerFeatures=k,t.combineClientFeatures=E,t.combineWindowFeatures=T,t.combineWorkspaceFeatures=R,t.combineLanguagesFeatures=P,t.combineFeatures=function(e,t){function n(e,t,n){return e&&t?n(e,t):e||t}return{__brand:"features",console:n(e.console,t.console,S),tracer:n(e.tracer,t.tracer,k),telemetry:n(e.telemetry,t.telemetry,x),client:n(e.client,t.client,E),window:n(e.window,t.window,T),workspace:n(e.workspace,t.workspace,R)}},t.createConnection=function(e,t,n,i){let o,c,u,d;return void 0!==e&&"features"===e.__brand&&(o=e,e=t,t=n,n=i),r.ConnectionStrategy.is(e)?d=e:(c=e,u=t,d=n),function(e,t,n,i){if(!e&&!t&&process.argv.length>2){let n,i,s=process.argv.slice(2);for(let a=0;a<s.length;a++){let c=s[a];if("--node-ipc"===c){e=new r.IPCMessageReader(process),t=new r.IPCMessageWriter(process);break}if("--stdio"===c){e=process.stdin,t=process.stdout;break}if("--socket"===c){n=parseInt(s[a+1]);break}if("--pipe"===c){i=s[a+1];break}var o=c.split("=");if("--socket"===o[0]){n=parseInt(o[1]);break}if("--pipe"===o[0]){i=o[1];break}}if(n){let i=r.createServerSocketTransport(n);e=i[0],t=i[1]}else if(i){let n=r.createServerPipeTransport(i);e=n[0],t=n[1]}}var c="Use arguments of createConnection or set command line parameters: '--node-ipc', '--stdio' or '--socket={number}'";if(!e)throw new Error("Connection input stream is not set. "+c);if(!t)throw new Error("Connection output stream is not set. "+c);if(a.func(e.read)&&a.func(e.on)){let t=e;t.on("end",()=>{process.exit(f?0:1)}),t.on("close",()=>{process.exit(f?0:1)})}const u=i&&i.console?new(i.console(h)):new h,d=r.createProtocolConnection(e,t,u,n);u.rawAttach(d);const m=i&&i.tracer?new(i.tracer(_)):new _,g=i&&i.telemetry?new(i.telemetry(b)):new b,S=i&&i.client?new(i.client(y)):new y,x=i&&i.window?new(i.window(p)):new p,k=i&&i.workspace?new(i.workspace(v)):new v,E=i&&i.languages?new(i.languages(w)):new w,T=[u,m,g,S,x,k,E];function R(e){return e instanceof Promise?e:a.thenable(e)?new Promise((t,n)=>{e.then(e=>t(e),e=>n(e))}):Promise.resolve(e)}let P,O,C,D={listen:()=>d.listen(),sendRequest:(e,...t)=>d.sendRequest(a.string(e)?e:e.method,...t),onRequest:(e,t)=>d.onRequest(e,t),sendNotification:(e,t)=>{const n=a.string(e)?e:e.method;1===arguments.length?d.sendNotification(n):d.sendNotification(n,t)},onNotification:(e,t)=>d.onNotification(e,t),onProgress:d.onProgress,sendProgress:d.sendProgress,onInitialize:e=>O=e,onInitialized:e=>d.onNotification(r.InitializedNotification.type,e),onShutdown:e=>P=e,onExit:e=>C=e,get console(){return u},get telemetry(){return g},get tracer(){return m},get client(){return S},get window(){return x},get workspace(){return k},get languages(){return E},onDidChangeConfiguration:e=>d.onNotification(r.DidChangeConfigurationNotification.type,e),onDidChangeWatchedFiles:e=>d.onNotification(r.DidChangeWatchedFilesNotification.type,e),__textDocumentSync:void 0,onDidOpenTextDocument:e=>d.onNotification(r.DidOpenTextDocumentNotification.type,e),onDidChangeTextDocument:e=>d.onNotification(r.DidChangeTextDocumentNotification.type,e),onDidCloseTextDocument:e=>d.onNotification(r.DidCloseTextDocumentNotification.type,e),onWillSaveTextDocument:e=>d.onNotification(r.WillSaveTextDocumentNotification.type,e),onWillSaveTextDocumentWaitUntil:e=>d.onRequest(r.WillSaveTextDocumentWaitUntilRequest.type,e),onDidSaveTextDocument:e=>d.onNotification(r.DidSaveTextDocumentNotification.type,e),sendDiagnostics:e=>d.sendNotification(r.PublishDiagnosticsNotification.type,e),onHover:e=>d.onRequest(r.HoverRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),void 0)),onCompletion:e=>d.onRequest(r.CompletionRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onCompletionResolve:e=>d.onRequest(r.CompletionResolveRequest.type,e),onSignatureHelp:e=>d.onRequest(r.SignatureHelpRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),void 0)),onDeclaration:e=>d.onRequest(r.DeclarationRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onDefinition:e=>d.onRequest(r.DefinitionRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onTypeDefinition:e=>d.onRequest(r.TypeDefinitionRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onImplementation:e=>d.onRequest(r.ImplementationRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onReferences:e=>d.onRequest(r.ReferencesRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onDocumentHighlight:e=>d.onRequest(r.DocumentHighlightRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onDocumentSymbol:e=>d.onRequest(r.DocumentSymbolRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onWorkspaceSymbol:e=>d.onRequest(r.WorkspaceSymbolRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onCodeAction:e=>d.onRequest(r.CodeActionRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onCodeLens:e=>d.onRequest(r.CodeLensRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onCodeLensResolve:e=>d.onRequest(r.CodeLensResolveRequest.type,(t,n)=>e(t,n)),onDocumentFormatting:e=>d.onRequest(r.DocumentFormattingRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),void 0)),onDocumentRangeFormatting:e=>d.onRequest(r.DocumentRangeFormattingRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),void 0)),onDocumentOnTypeFormatting:e=>d.onRequest(r.DocumentOnTypeFormattingRequest.type,(t,n)=>e(t,n)),onRenameRequest:e=>d.onRequest(r.RenameRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),void 0)),onPrepareRename:e=>d.onRequest(r.PrepareRenameRequest.type,(t,n)=>e(t,n)),onDocumentLinks:e=>d.onRequest(r.DocumentLinkRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onDocumentLinkResolve:e=>d.onRequest(r.DocumentLinkResolveRequest.type,(t,n)=>e(t,n)),onDocumentColor:e=>d.onRequest(r.DocumentColorRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onColorPresentation:e=>d.onRequest(r.ColorPresentationRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onFoldingRanges:e=>d.onRequest(r.FoldingRangeRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onSelectionRanges:e=>d.onRequest(r.SelectionRangeRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),s.attachPartialResult(d,t))),onExecuteCommand:e=>d.onRequest(r.ExecuteCommandRequest.type,(t,n)=>e(t,n,s.attachWorkDone(d,t),void 0)),dispose:()=>d.dispose()};for(let e of T)e.attach(D);return d.onRequest(r.InitializeRequest.type,e=>{const t=e.processId;a.number(t)&&void 0===l&&setInterval(()=>{try{process.kill(t,0)}catch(e){process.exit(f?0:1)}},3e3),a.string(e.trace)&&(m.trace=r.Trace.fromString(e.trace));for(let t of T)t.initialize(e.capabilities);if(O)return R(O(e,(new r.CancellationTokenSource).token,s.attachWorkDone(d,e),void 0)).then(e=>{if(e instanceof r.ResponseError)return e;let t=e;t||(t={capabilities:{}});let n=t.capabilities;n||(n={},t.capabilities=n),void 0===n.textDocumentSync||null===n.textDocumentSync?n.textDocumentSync=a.number(D.__textDocumentSync)?D.__textDocumentSync:r.TextDocumentSyncKind.None:a.number(n.textDocumentSync)||a.number(n.textDocumentSync.change)||(n.textDocumentSync.change=a.number(D.__textDocumentSync)?D.__textDocumentSync:r.TextDocumentSyncKind.None);for(let e of T)e.fillServerCapabilities(n);return t});{let e={capabilities:{textDocumentSync:r.TextDocumentSyncKind.None}};for(let t of T)t.fillServerCapabilities(e.capabilities);return e}}),d.onRequest(r.ShutdownRequest.type,()=>(f=!0,P?P((new r.CancellationTokenSource).token):void 0)),d.onNotification(r.ExitNotification.type,()=>{try{C&&C()}finally{f?process.exit(0):process.exit(1)}}),d.onNotification(r.SetTraceNotification.type,e=>{m.trace=r.Trace.fromString(e.value)}),D}(c,u,d,o)};const O=n(484),C=n(485);!function(e){e.all={__brand:"features",languages:P(O.CallHierarchyFeature,C.SemanticTokensFeature)},e.SemanticTokensBuilder=C.SemanticTokensBuilder}(t.ProposedFeatures||(t.ProposedFeatures={}))},,,,function(e,t,n){"use strict";e.exports={copySync:n(120)}},function(e,t){e.exports=e=>{const t=process.versions.node.split(".").map(e=>parseInt(e,10));return e=e.split(".").map(e=>parseInt(e,10)),t[0]>e[0]||t[0]===e[0]&&(t[1]>e[1]||t[1]===e[1]&&t[2]>=e[2])}},function(e,t,n){"use strict";const r=n(4);e.exports={utimesMillis:function(e,t,n,i){r.open(e,"r+",(e,o)=>{if(e)return i(e);r.futimes(o,t,n,e=>{r.close(o,t=>{i&&i(e||t)})})})},utimesMillisSync:function(e,t,n){const i=r.openSync(e,"r+");return r.futimesSync(i,t,n),r.closeSync(i)}}},function(e,t,n){"use strict";const r=n(8).fromCallback;e.exports={copy:r(n(122))}},function(e,t,n){"use strict";const r=n(142),i=n(143),o=n(144);e.exports={err:r,mapHttpRequest:i.mapHttpRequest,mapHttpResponse:o.mapHttpResponse,req:i.reqSerializer,res:o.resSerializer,wrapErrorSerializer:function(e){return e===r?e:function(t){return e(r(t))}},wrapRequestSerializer:function(e){return e===i.reqSerializer?e:function(t){return e(i.reqSerializer(t))}},wrapResponseSerializer:function(e){return e===o.resSerializer?e:function(t){return e(o.resSerializer(t))}}}},function(e,t,n){"use strict";const r=n(145),{redactFmtSym:i,wildcardFirstSym:o}=n(21),{rx:s,validator:a}=r,c=a({ERR_PATHS_MUST_BE_STRINGS:()=>"pino – redacted paths must be strings",ERR_INVALID_PATH:e=>`pino – redact paths array contains an invalid path (${e})`}),u="[Redacted]";e.exports=function(e,t){const{paths:n,censor:a}=function(e){if(Array.isArray(e))return c(e={paths:e,censor:u}),e;let{paths:t,censor:n=u,remove:r}=e;if(!1===Array.isArray(t))throw Error("pino – redact must contain an array of strings");return!0===r&&(n=void 0),c({paths:t,censor:n}),{paths:t,censor:n}}(e),l=n.reduce((e,t)=>{s.lastIndex=0;const n=s.exec(t),r=s.exec(t);let i=void 0!==n[1]?n[1].replace(/^(?:"|'|`)(.*)(?:"|'|`)$/,"$1"):n[0];if("*"===i&&(i=o),null===r)return e[i]=null,e;if(null===e[i])return e;const{index:a}=r,c=""+t.substr(a,t.length-1);return e[i]=e[i]||[],i!==o&&0===e[i].length&&e[i].push(...e[o]||[]),i===o&&Object.keys(e).forEach((function(t){e[t]&&e[t].push(c)})),e[i].push(c),e},{}),f={[i]:r({paths:n,censor:a,serialize:t,strict:!1})};return[...Object.keys(l),...Object.getOwnPropertySymbols(l)].reduce((e,n)=>{if(null===l[n])e[n]=e=>((...e)=>t("function"==typeof a?a(...e):a))(e,[n]);else{const i="function"==typeof a?(e,t)=>a(e,[n,...t]):a;e[n]=r({paths:l[n],censor:i,serialize:t,strict:!1})}return e},f)}},function(e,t,n){"use strict";function r(e,t,n,r,i,o,s){const a=r.length,c=a-1,u=t;var l,f,d,h,p,m=-1,g=null,y=!0;if(d=l=e[t],"object"!=typeof l)return{value:null,parent:null,exists:y};for(;null!=l&&++m<a;){if(g=d,!((t=r[m])in l)){y=!1;break}if(d=l[t],f=m!==c?d:o?s?i(d,[...n,u,...r]):i(d):i,l[t]=(h=l,p=t,Object.prototype.hasOwnProperty.call(h,p)&&f===d||void 0===f&&void 0!==i?l[t]:f),"object"!=typeof(l=l[t]))break}return{value:d,parent:g,exists:y}}function i(e,t){for(var n=-1,r=t.length,i=e;null!=i&&++n<r;)i=i[t[n]];return i}e.exports={groupRedact:function(e,t,n,r,o){const s=i(e,t);if(null==s)return{keys:null,values:null,target:null,flat:!0};const a=Object.keys(s),c=a.length,u=t.length,l=o?[...t]:void 0,f=new Array(c);for(var d=0;d<c;d++){const e=a[d];f[d]=s[e],o?(l[u]=e,s[e]=n(s[e],l)):s[e]=r?n(s[e]):n}return{keys:a,values:f,target:s,flat:!0}},groupRestore:function({keys:e,values:t,target:n}){if(null==n)return;const r=e.length;for(var i=0;i<r;i++){n[e[i]]=t[i]}},nestedRedact:function(e,t,n,o,s,a,c){const u=i(t,n);if(null==u)return;const l=Object.keys(u),f=l.length;for(var d=0;d<f;d++){const t=l[d],{value:i,parent:f,exists:h}=r(u,t,n,o,s,a,c);!0===h&&null!==f&&e.push({key:o[o.length-1],target:f,value:i})}return e},nestedRestore:function(e){const t=e.length;for(var n=0;n<t;n++){const{key:t,target:r,value:i}=e[n];r[t]=i}}}},function(e,t,n){"use strict";const r=n(1),i=n(23),o=n(43),s=n(6).inherits,a=n(154),c=16777216;function u(e,t){function n(n,r){if(n)return t._reopening=!1,t._writing=!1,t._opening=!1,void(t.sync?process.nextTick(()=>{t.listenerCount("error")>0&&t.emit("error",n)}):t.emit("error",n));if(t.fd=r,t.file=e,t._reopening=!1,t._opening=!1,t._writing=!1,t.sync?process.nextTick(()=>t.emit("ready")):t.emit("ready"),t._reopening)return;const i=t._buf.length;i>0&&i>t.minLength&&!t.destroyed&&d(t)}if(t._opening=!0,t._writing=!0,t._asyncDrainScheduled=!1,t.sync)try{n(null,r.openSync(e,"a"))}catch(e){throw n(e),e}else r.open(e,"a",n)}function l(e){if(!(this instanceof l))return new l(e);let{fd:t,dest:n,minLength:i,sync:o}=e||{};if(t=t||n,this._buf="",this.fd=-1,this._writing=!1,this._writingBuf="",this._ending=!1,this._reopening=!1,this._asyncDrainScheduled=!1,this.file=null,this.destroyed=!1,this.sync=o||!1,this.minLength=i||0,"number"==typeof t)this.fd=t,process.nextTick(()=>this.emit("ready"));else{if("string"!=typeof t)throw new Error("SonicBoom supports only file descriptors and files");u(t,this)}this.release=(e,t)=>{if(e){if("EAGAIN"===e.code)if(this.sync)try{a(100),this.release(void 0,0)}catch(e){this.release(e)}else setTimeout(()=>{r.write(this.fd,this._writingBuf,"utf8",this.release)},100);else this._buf=this._writingBuf+this._buf,this._writingBuf="",this._writing=!1,this.emit("error",e);return}if(this._writingBuf.length!==t){if(this._writingBuf=this._writingBuf.slice(t),!this.sync)return void r.write(this.fd,this._writingBuf,"utf8",this.release);try{do{t=r.writeSync(this.fd,this._writingBuf,"utf8"),this._writingBuf=this._writingBuf.slice(t)}while(0!==this._writingBuf.length)}catch(e){return void this.release(e)}}if(this._writingBuf="",this.destroyed)return;const n=this._buf.length;this._reopening?(this._writing=!1,this._reopening=!1,this.reopen()):n>0&&n>this.minLength?d(this):this._ending?n>0?d(this):(this._writing=!1,h(this)):(this._writing=!1,this.sync?this._asyncDrainScheduled||(this._asyncDrainScheduled=!0,process.nextTick(f,this)):this.emit("drain"))},this.on("newListener",(function(e){"drain"===e&&(this._asyncDrainScheduled=!1)}))}function f(e){e.listenerCount("drain")>0&&(e._asyncDrainScheduled=!1,e.emit("drain"))}function d(e){e._writing=!0;let t=e._buf;const n=e.release;if(t.length>c?(t=t.slice(0,c),e._buf=e._buf.slice(c)):e._buf="",o(t),e._writingBuf=t,e.sync)try{n(null,r.writeSync(e.fd,t,"utf8"))}catch(e){n(e)}else r.write(e.fd,t,"utf8",n)}function h(e){-1!==e.fd?(r.close(e.fd,t=>{t?e.emit("error",t):(e._ending&&!e._writing&&e.emit("finish"),e.emit("close"))}),e.destroyed=!0,e._buf=""):e.once("ready",h.bind(null,e))}s(l,i),l.prototype.write=function(e){if(this.destroyed)throw new Error("SonicBoom destroyed");this._buf+=e;const t=this._buf.length;return!this._writing&&t>this.minLength&&d(this),t<16384},l.prototype.flush=function(){if(this.destroyed)throw new Error("SonicBoom destroyed");this._writing||this.minLength<=0||d(this)},l.prototype.reopen=function(e){if(this.destroyed)throw new Error("SonicBoom destroyed");if(this._opening)return void this.once("ready",()=>{this.reopen(e)});if(this._ending)return;if(!this.file)throw new Error("Unable to reopen a file descriptor, you must pass a file to SonicBoom");if(this._reopening=!0,this._writing)return;const t=this.fd;this.once("ready",()=>{t!==this.fd&&r.close(t,e=>{if(e)return this.emit("error",e)})}),u(e||this.file,this)},l.prototype.end=function(){if(this.destroyed)throw new Error("SonicBoom destroyed");this._opening?this.once("ready",()=>{this.end()}):this._ending||(this._ending=!0,!this._writing&&this._buf.length>0&&this.fd>=0?d(this):this._writing||h(this))},l.prototype.flushSync=function(){if(this.destroyed)throw new Error("SonicBoom destroyed");if(this.fd<0)throw new Error("sonic boom is not ready yet");for(;this._buf.length>0;)try{r.writeSync(this.fd,this._buf,"utf8"),this._buf=""}catch(e){if("EAGAIN"!==e.code)throw e;a(100)}},l.prototype.destroy=function(){this.destroyed||h(this)},e.exports=l},function(e,t,n){"use strict";const r=n(43),{lsCacheSym:i,levelValSym:o,useOnlyCustomLevelsSym:s,streamSym:a,formattersSym:c,hooksSym:u}=n(21),{noop:l,genLog:f}=n(44),d={trace:10,debug:20,info:30,warn:40,error:50,fatal:60},h={fatal:e=>{const t=f(d.fatal,e);return function(...e){const n=this[a];if(t.call(this,...e),"function"==typeof n.flushSync)try{n.flushSync()}catch(e){}}},error:e=>f(d.error,e),warn:e=>f(d.warn,e),info:e=>f(d.info,e),debug:e=>f(d.debug,e),trace:e=>f(d.trace,e)},p=Object.keys(d).reduce((e,t)=>(e[d[t]]=t,e),{}),m=Object.keys(p).reduce((e,t)=>(e[t]=r('{"level":'+Number(t)),e),{});function g(e,t){if(t)return!1;switch(e){case"fatal":case"error":case"warn":case"info":case"debug":case"trace":return!0;default:return!1}}e.exports={initialLsCache:m,genLsCache:function(e){const t=e[c].level,{labels:n}=e.levels,r={};for(const e in n){const i=t(n[e],Number(e));r[e]=JSON.stringify(i).slice(0,-1)}return e[i]=r,e},levelMethods:h,getLevel:function(e){const{levels:t,levelVal:n}=this;return t&&t.labels?t.labels[n]:""},setLevel:function(e){const{labels:t,values:n}=this.levels;if("number"==typeof e){if(void 0===t[e])throw Error("unknown level value"+e);e=t[e]}if(void 0===n[e])throw Error("unknown level "+e);const r=this[o],i=this[o]=n[e],a=this[s],c=this[u].logMethod;for(const e in n)i>n[e]?this[e]=l:this[e]=g(e,a)?h[e](c):f(n[e],c);this.emit("level-change",e,i,t[r],r)},isLevelEnabled:function(e){const{values:t}=this.levels,n=t[e];return void 0!==n&&n>=this[o]},mappings:function(e=null,t=!1){const n=e?Object.keys(e).reduce((t,n)=>(t[e[n]]=n,t),{}):null;return{labels:Object.assign(Object.create(Object.prototype,{Infinity:{value:"silent"}}),t?null:p,n),values:Object.assign(Object.create(Object.prototype,{silent:{value:1/0}}),t?null:d,e)}},assertNoLevelCollisions:function(e,t){const{labels:n,values:r}=e;for(const e in t){if(e in r)throw Error("levels cannot be overridden");if(t[e]in n)throw Error("pre-existing level values cannot be used for new levels")}},assertDefaultLevelFound:function(e,t,n){if("number"!=typeof e){if(!(e in Object.assign(Object.create(Object.prototype,{silent:{value:1/0}}),n?null:d,t)))throw Error(`default level:${e} must be included in custom levels`)}else if(![].concat(Object.keys(t||{}).map(e=>t[e]),n?[]:Object.keys(p).map(e=>+e),1/0).includes(e))throw Error(`default level:${e} must be included in custom levels`)}}},function(e,t,n){"use strict";const r=n(160),{stdout:i,stderr:o}=n(165),{stringReplaceAll:s,stringEncaseCRLFWithFirstIndex:a}=n(168),{isArray:c}=Array,u=["ansi","ansi","ansi256","ansi16m"],l=Object.create(null);class f{constructor(e){return d(e)}}const d=e=>{const t={};return((e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");const n=i?i.level:0;e.level=void 0===t.level?n:t.level})(t,e),t.template=(...e)=>_(t.template,...e),Object.setPrototypeOf(t,h.prototype),Object.setPrototypeOf(t.template,t),t.template.constructor=()=>{throw new Error("`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.")},t.template.Instance=f,t.template};function h(e){return d(e)}for(const[e,t]of Object.entries(r))l[e]={get(){const n=y(this,g(t.open,t.close,this._styler),this._isEmpty);return Object.defineProperty(this,e,{value:n}),n}};l.visible={get(){const e=y(this,this._styler,!0);return Object.defineProperty(this,"visible",{value:e}),e}};const p=["rgb","hex","keyword","hsl","hsv","hwb","ansi","ansi256"];for(const e of p)l[e]={get(){const{level:t}=this;return function(...n){const i=g(r.color[u[t]][e](...n),r.color.close,this._styler);return y(this,i,this._isEmpty)}}};for(const e of p)l["bg"+e[0].toUpperCase()+e.slice(1)]={get(){const{level:t}=this;return function(...n){const i=g(r.bgColor[u[t]][e](...n),r.bgColor.close,this._styler);return y(this,i,this._isEmpty)}}};const m=Object.defineProperties(()=>{},{...l,level:{enumerable:!0,get(){return this._generator.level},set(e){this._generator.level=e}}}),g=(e,t,n)=>{let r,i;return void 0===n?(r=e,i=t):(r=n.openAll+e,i=t+n.closeAll),{open:e,close:t,openAll:r,closeAll:i,parent:n}},y=(e,t,n)=>{const r=(...e)=>c(e[0])&&c(e[0].raw)?v(r,_(r,...e)):v(r,1===e.length?""+e[0]:e.join(" "));return Object.setPrototypeOf(r,m),r._generator=e,r._styler=t,r._isEmpty=n,r},v=(e,t)=>{if(e.level<=0||!t)return e._isEmpty?"":t;let n=e._styler;if(void 0===n)return t;const{openAll:r,closeAll:i}=n;if(-1!==t.indexOf(""))for(;void 0!==n;)t=s(t,n.close,n.open),n=n.parent;const o=t.indexOf("\n");return-1!==o&&(t=a(t,i,r,o)),r+t+i};let b;const _=(e,...t)=>{const[r]=t;if(!c(r)||!c(r.raw))return t.join(" ");const i=t.slice(1),o=[r.raw[0]];for(let e=1;e<r.length;e++)o.push(String(i[e-1]).replace(/[{}\\]/g,"\\$&"),String(r.raw[e]));return void 0===b&&(b=n(169)),b(e,o.join(""))};Object.defineProperties(h.prototype,l);const w=h();w.supportsColor=i,w.stderr=h({level:o?o.level:0}),w.stderr.supportsColor=o,e.exports=w},function(e,t,n){const r=n(163),i={};for(const e of Object.keys(r))i[r[e]]=e;const o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=o;for(const e of Object.keys(o)){if(!("channels"in o[e]))throw new Error("missing channels property: "+e);if(!("labels"in o[e]))throw new Error("missing channel labels property: "+e);if(o[e].labels.length!==o[e].channels)throw new Error("channel and label counts mismatch: "+e);const{channels:t,labels:n}=o[e];delete o[e].channels,delete o[e].labels,Object.defineProperty(o[e],"channels",{value:t}),Object.defineProperty(o[e],"labels",{value:n})}o.rgb.hsl=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,i=Math.min(t,n,r),o=Math.max(t,n,r),s=o-i;let a,c;o===i?a=0:t===o?a=(n-r)/s:n===o?a=2+(r-t)/s:r===o&&(a=4+(t-n)/s),a=Math.min(60*a,360),a<0&&(a+=360);const u=(i+o)/2;return c=o===i?0:u<=.5?s/(o+i):s/(2-o-i),[a,100*c,100*u]},o.rgb.hsv=function(e){let t,n,r,i,o;const s=e[0]/255,a=e[1]/255,c=e[2]/255,u=Math.max(s,a,c),l=u-Math.min(s,a,c),f=function(e){return(u-e)/6/l+.5};return 0===l?(i=0,o=0):(o=l/u,t=f(s),n=f(a),r=f(c),s===u?i=r-n:a===u?i=1/3+t-r:c===u&&(i=2/3+n-t),i<0?i+=1:i>1&&(i-=1)),[360*i,100*o,100*u]},o.rgb.hwb=function(e){const t=e[0],n=e[1];let r=e[2];const i=o.rgb.hsl(e)[0],s=1/255*Math.min(t,Math.min(n,r));return r=1-1/255*Math.max(t,Math.max(n,r)),[i,100*s,100*r]},o.rgb.cmyk=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,i=Math.min(1-t,1-n,1-r);return[100*((1-t-i)/(1-i)||0),100*((1-n-i)/(1-i)||0),100*((1-r-i)/(1-i)||0),100*i]},o.rgb.keyword=function(e){const t=i[e];if(t)return t;let n,o=1/0;for(const t of Object.keys(r)){const i=(a=r[t],((s=e)[0]-a[0])**2+(s[1]-a[1])**2+(s[2]-a[2])**2);i<o&&(o=i,n=t)}var s,a;return n},o.keyword.rgb=function(e){return r[e]},o.rgb.xyz=function(e){let t=e[0]/255,n=e[1]/255,r=e[2]/255;return t=t>.04045?((t+.055)/1.055)**2.4:t/12.92,n=n>.04045?((n+.055)/1.055)**2.4:n/12.92,r=r>.04045?((r+.055)/1.055)**2.4:r/12.92,[100*(.4124*t+.3576*n+.1805*r),100*(.2126*t+.7152*n+.0722*r),100*(.0193*t+.1192*n+.9505*r)]},o.rgb.lab=function(e){const t=o.rgb.xyz(e);let n=t[0],r=t[1],i=t[2];return n/=95.047,r/=100,i/=108.883,n=n>.008856?n**(1/3):7.787*n+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,i=i>.008856?i**(1/3):7.787*i+16/116,[116*r-16,500*(n-r),200*(r-i)]},o.hsl.rgb=function(e){const t=e[0]/360,n=e[1]/100,r=e[2]/100;let i,o,s;if(0===n)return s=255*r,[s,s,s];i=r<.5?r*(1+n):r+n-r*n;const a=2*r-i,c=[0,0,0];for(let e=0;e<3;e++)o=t+1/3*-(e-1),o<0&&o++,o>1&&o--,s=6*o<1?a+6*(i-a)*o:2*o<1?i:3*o<2?a+(i-a)*(2/3-o)*6:a,c[e]=255*s;return c},o.hsl.hsv=function(e){const t=e[0];let n=e[1]/100,r=e[2]/100,i=n;const o=Math.max(r,.01);return r*=2,n*=r<=1?r:2-r,i*=o<=1?o:2-o,[t,100*(0===r?2*i/(o+i):2*n/(r+n)),(r+n)/2*100]},o.hsv.rgb=function(e){const t=e[0]/60,n=e[1]/100;let r=e[2]/100;const i=Math.floor(t)%6,o=t-Math.floor(t),s=255*r*(1-n),a=255*r*(1-n*o),c=255*r*(1-n*(1-o));switch(r*=255,i){case 0:return[r,c,s];case 1:return[a,r,s];case 2:return[s,r,c];case 3:return[s,a,r];case 4:return[c,s,r];case 5:return[r,s,a]}},o.hsv.hsl=function(e){const t=e[0],n=e[1]/100,r=e[2]/100,i=Math.max(r,.01);let o,s;s=(2-n)*r;const a=(2-n)*i;return o=n*i,o/=a<=1?a:2-a,o=o||0,s/=2,[t,100*o,100*s]},o.hwb.rgb=function(e){const t=e[0]/360;let n=e[1]/100,r=e[2]/100;const i=n+r;let o;i>1&&(n/=i,r/=i);const s=Math.floor(6*t),a=1-r;o=6*t-s,0!=(1&s)&&(o=1-o);const c=n+o*(a-n);let u,l,f;switch(s){default:case 6:case 0:u=a,l=c,f=n;break;case 1:u=c,l=a,f=n;break;case 2:u=n,l=a,f=c;break;case 3:u=n,l=c,f=a;break;case 4:u=c,l=n,f=a;break;case 5:u=a,l=n,f=c}return[255*u,255*l,255*f]},o.cmyk.rgb=function(e){const t=e[0]/100,n=e[1]/100,r=e[2]/100,i=e[3]/100;return[255*(1-Math.min(1,t*(1-i)+i)),255*(1-Math.min(1,n*(1-i)+i)),255*(1-Math.min(1,r*(1-i)+i))]},o.xyz.rgb=function(e){const t=e[0]/100,n=e[1]/100,r=e[2]/100;let i,o,s;return i=3.2406*t+-1.5372*n+-.4986*r,o=-.9689*t+1.8758*n+.0415*r,s=.0557*t+-.204*n+1.057*r,i=i>.0031308?1.055*i**(1/2.4)-.055:12.92*i,o=o>.0031308?1.055*o**(1/2.4)-.055:12.92*o,s=s>.0031308?1.055*s**(1/2.4)-.055:12.92*s,i=Math.min(Math.max(0,i),1),o=Math.min(Math.max(0,o),1),s=Math.min(Math.max(0,s),1),[255*i,255*o,255*s]},o.xyz.lab=function(e){let t=e[0],n=e[1],r=e[2];return t/=95.047,n/=100,r/=108.883,t=t>.008856?t**(1/3):7.787*t+16/116,n=n>.008856?n**(1/3):7.787*n+16/116,r=r>.008856?r**(1/3):7.787*r+16/116,[116*n-16,500*(t-n),200*(n-r)]},o.lab.xyz=function(e){let t,n,r;n=(e[0]+16)/116,t=e[1]/500+n,r=n-e[2]/200;const i=n**3,o=t**3,s=r**3;return n=i>.008856?i:(n-16/116)/7.787,t=o>.008856?o:(t-16/116)/7.787,r=s>.008856?s:(r-16/116)/7.787,t*=95.047,n*=100,r*=108.883,[t,n,r]},o.lab.lch=function(e){const t=e[0],n=e[1],r=e[2];let i;return i=360*Math.atan2(r,n)/2/Math.PI,i<0&&(i+=360),[t,Math.sqrt(n*n+r*r),i]},o.lch.lab=function(e){const t=e[0],n=e[1],r=e[2]/360*2*Math.PI;return[t,n*Math.cos(r),n*Math.sin(r)]},o.rgb.ansi16=function(e,t=null){const[n,r,i]=e;let s=null===t?o.rgb.hsv(e)[2]:t;if(s=Math.round(s/50),0===s)return 30;let a=30+(Math.round(i/255)<<2|Math.round(r/255)<<1|Math.round(n/255));return 2===s&&(a+=60),a},o.hsv.ansi16=function(e){return o.rgb.ansi16(o.hsv.rgb(e),e[2])},o.rgb.ansi256=function(e){const t=e[0],n=e[1],r=e[2];return t===n&&n===r?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5)},o.ansi16.rgb=function(e){let t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),t=t/10.5*255,[t,t,t];const n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},o.ansi256.rgb=function(e){if(e>=232){const t=10*(e-232)+8;return[t,t,t]}let t;return e-=16,[Math.floor(e/36)/5*255,Math.floor((t=e%36)/6)/5*255,t%6/5*255]},o.rgb.hex=function(e){const t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},o.hex.rgb=function(e){const t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];let n=t[0];3===t[0].length&&(n=n.split("").map(e=>e+e).join(""));const r=parseInt(n,16);return[r>>16&255,r>>8&255,255&r]},o.rgb.hcg=function(e){const t=e[0]/255,n=e[1]/255,r=e[2]/255,i=Math.max(Math.max(t,n),r),o=Math.min(Math.min(t,n),r),s=i-o;let a,c;return a=s<1?o/(1-s):0,c=s<=0?0:i===t?(n-r)/s%6:i===n?2+(r-t)/s:4+(t-n)/s,c/=6,c%=1,[360*c,100*s,100*a]},o.hsl.hcg=function(e){const t=e[1]/100,n=e[2]/100,r=n<.5?2*t*n:2*t*(1-n);let i=0;return r<1&&(i=(n-.5*r)/(1-r)),[e[0],100*r,100*i]},o.hsv.hcg=function(e){const t=e[1]/100,n=e[2]/100,r=t*n;let i=0;return r<1&&(i=(n-r)/(1-r)),[e[0],100*r,100*i]},o.hcg.rgb=function(e){const t=e[0]/360,n=e[1]/100,r=e[2]/100;if(0===n)return[255*r,255*r,255*r];const i=[0,0,0],o=t%1*6,s=o%1,a=1-s;let c=0;switch(Math.floor(o)){case 0:i[0]=1,i[1]=s,i[2]=0;break;case 1:i[0]=a,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=s;break;case 3:i[0]=0,i[1]=a,i[2]=1;break;case 4:i[0]=s,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=a}return c=(1-n)*r,[255*(n*i[0]+c),255*(n*i[1]+c),255*(n*i[2]+c)]},o.hcg.hsv=function(e){const t=e[1]/100,n=t+e[2]/100*(1-t);let r=0;return n>0&&(r=t/n),[e[0],100*r,100*n]},o.hcg.hsl=function(e){const t=e[1]/100,n=e[2]/100*(1-t)+.5*t;let r=0;return n>0&&n<.5?r=t/(2*n):n>=.5&&n<1&&(r=t/(2*(1-n))),[e[0],100*r,100*n]},o.hcg.hwb=function(e){const t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},o.hwb.hcg=function(e){const t=e[1]/100,n=1-e[2]/100,r=n-t;let i=0;return r<1&&(i=(n-r)/(1-r)),[e[0],100*r,100*i]},o.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},o.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},o.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},o.gray.hsl=function(e){return[0,0,e[0]]},o.gray.hsv=o.gray.hsl,o.gray.hwb=function(e){return[0,100,e[0]]},o.gray.cmyk=function(e){return[0,0,0,e[0]]},o.gray.lab=function(e){return[e[0],0,0]},o.gray.hex=function(e){const t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},o.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},function(e,t,n){"use strict";const{LEVELS:r,LEVEL_NAMES:i}=n(45),o=e=>e,s={default:o,60:o,50:o,40:o,30:o,20:o,10:o,message:o,greyMessage:o},a=new(n(69).Instance)({level:3}),c={default:a.white,60:a.bgRed,50:a.red,40:a.yellow,30:a.green,20:a.blue,10:a.grey,message:a.cyan,greyMessage:a.grey};function u(e,t){if(Number.isInteger(+e))return Object.prototype.hasOwnProperty.call(r,e)?t[e](r[e]):t.default(r.default);const n=i[e.toLowerCase()]||"default";return t[n](r[n])}function l(e){return u(e,s)}function f(e){return u(e,c)}l.message=s.message,l.greyMessage=s.greyMessage,f.message=c.message,f.greyMessage=c.greyMessage,e.exports=function(e=!1){return e?f:l}},function(e,t,n){"use strict";const{version:r}=n(175);e.exports={version:r}},,function(e,t){e.exports=require("net")},,,,,,,,,,,,function(e,t,n){const r=n(1),i=n(0);function o(e){console.log("[dotenv][DEBUG] "+e)}const s=/^\s*([\w.-]+)\s*=\s*(.*)?\s*$/,a=/\\n/g,c=/\n|\r|\r\n/;function u(e,t){const n=Boolean(t&&t.debug),r={};return e.toString().split(c).forEach((function(e,t){const i=e.match(s);if(null!=i){const e=i[1];let t=i[2]||"";const n=t.length-1,o='"'===t[0]&&'"'===t[n];"'"===t[0]&&"'"===t[n]||o?(t=t.substring(1,n),o&&(t=t.replace(a,"\n"))):t=t.trim(),r[e]=t}else n&&o(`did not match key and value when parsing line ${t+1}: ${e}`)})),r}e.exports.config=function(e){let t=i.resolve(process.cwd(),".env"),n="utf8",s=!1;e&&(null!=e.path&&(t=e.path),null!=e.encoding&&(n=e.encoding),null!=e.debug&&(s=!0));try{const e=u(r.readFileSync(t,{encoding:n}),{debug:s});return Object.keys(e).forEach((function(t){Object.prototype.hasOwnProperty.call(process.env,t)?s&&o(`"${t}" is already defined in \`process.env\` and will not be overwritten`):process.env[t]=e[t]})),{parsed:e}}catch(e){return{error:e}}},e.exports.parse=u},,,,,function(e,t){e.exports=require("crypto")},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.UpdateLanguageFeaturesOnDocumentChanged=t.InitializeLanguageServerFeatures=void 0;const i=n(55),o=n(0),s=n(3),a=n(490),c=n(301),u=n(491),l=n(302),f=n(296),d=n(36),{readFile:h,writeFile:p}=n(1).promises;t.InitializeLanguageServerFeatures=e=>r(void 0,void 0,void 0,(function*(){f.connection.sendNotification("initialize-ls-features");const t=i.fileURLToPath(e[0].uri),n=yield d.getFilesPathsFromFolder(t),r=c.filterVBAFiles(n),a=yield l.createDocument(r);if(void 0!==a){const e=m(a),t=s.tmpdir(),n=o.join(t,"\\auto.json");yield p(n,JSON.stringify(e))}})),t.UpdateLanguageFeaturesOnDocumentChanged=e=>r(void 0,void 0,void 0,(function*(){const t=a.documentParseService(e),n=yield u.updateParsedItem(t);if(n){const e=s.tmpdir(),t=o.join(e,"\\auto.json");yield p(t,JSON.stringify(n))}}));const m=e=>{const t=[];return e.forEach(e=>{const n=a.documentParseService(e);t.push(...n)}),t}},,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){var r=n(117),i=process.cwd,o=null,s=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return o||(o=i.call(process)),o};try{process.cwd()}catch(e){}var a=process.chdir;process.chdir=function(e){o=null,a.call(process,e)},e.exports=function(e){var t,n;function i(t){return t?function(n,r,i){return t.call(e,n,r,(function(e){f(e)&&(e=null),i&&i.apply(this,arguments)}))}:t}function o(t){return t?function(n,r){try{return t.call(e,n,r)}catch(e){if(!f(e))throw e}}:t}function a(t){return t?function(n,r,i,o){return t.call(e,n,r,i,(function(e){f(e)&&(e=null),o&&o.apply(this,arguments)}))}:t}function c(t){return t?function(n,r,i){try{return t.call(e,n,r,i)}catch(e){if(!f(e))throw e}}:t}function u(t){return t?function(n,r,i){function o(e,t){t&&(t.uid<0&&(t.uid+=4294967296),t.gid<0&&(t.gid+=4294967296)),i&&i.apply(this,arguments)}return"function"==typeof r&&(i=r,r=null),r?t.call(e,n,r,o):t.call(e,n,o)}:t}function l(t){return t?function(n,r){var i=r?t.call(e,n,r):t.call(e,n);return i.uid<0&&(i.uid+=4294967296),i.gid<0&&(i.gid+=4294967296),i}:t}function f(e){return!e||"ENOSYS"===e.code||!(process.getuid&&0===process.getuid()||"EINVAL"!==e.code&&"EPERM"!==e.code)}r.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&function(e){e.lchmod=function(t,n,i){e.open(t,r.O_WRONLY|r.O_SYMLINK,n,(function(t,r){t?i&&i(t):e.fchmod(r,n,(function(t){e.close(r,(function(e){i&&i(t||e)}))}))}))},e.lchmodSync=function(t,n){var i,o=e.openSync(t,r.O_WRONLY|r.O_SYMLINK,n),s=!0;try{i=e.fchmodSync(o,n),s=!1}finally{if(s)try{e.closeSync(o)}catch(e){}else e.closeSync(o)}return i}}(e),e.lutimes||function(e){r.hasOwnProperty("O_SYMLINK")?(e.lutimes=function(t,n,i,o){e.open(t,r.O_SYMLINK,(function(t,r){t?o&&o(t):e.futimes(r,n,i,(function(t){e.close(r,(function(e){o&&o(t||e)}))}))}))},e.lutimesSync=function(t,n,i){var o,s=e.openSync(t,r.O_SYMLINK),a=!0;try{o=e.futimesSync(s,n,i),a=!1}finally{if(a)try{e.closeSync(s)}catch(e){}else e.closeSync(s)}return o}):(e.lutimes=function(e,t,n,r){r&&process.nextTick(r)},e.lutimesSync=function(){})}(e),e.chown=a(e.chown),e.fchown=a(e.fchown),e.lchown=a(e.lchown),e.chmod=i(e.chmod),e.fchmod=i(e.fchmod),e.lchmod=i(e.lchmod),e.chownSync=c(e.chownSync),e.fchownSync=c(e.fchownSync),e.lchownSync=c(e.lchownSync),e.chmodSync=o(e.chmodSync),e.fchmodSync=o(e.fchmodSync),e.lchmodSync=o(e.lchmodSync),e.stat=u(e.stat),e.fstat=u(e.fstat),e.lstat=u(e.lstat),e.statSync=l(e.statSync),e.fstatSync=l(e.fstatSync),e.lstatSync=l(e.lstatSync),e.lchmod||(e.lchmod=function(e,t,n){n&&process.nextTick(n)},e.lchmodSync=function(){}),e.lchown||(e.lchown=function(e,t,n,r){r&&process.nextTick(r)},e.lchownSync=function(){}),"win32"===s&&(e.rename=(t=e.rename,function(n,r,i){var o=Date.now(),s=0;t(n,r,(function a(c){if(c&&("EACCES"===c.code||"EPERM"===c.code)&&Date.now()-o<6e4)return setTimeout((function(){e.stat(r,(function(e,o){e&&"ENOENT"===e.code?t(n,r,a):i(c)}))}),s),void(s<100&&(s+=10));i&&i(c)}))})),e.read=function(t){function n(n,r,i,o,s,a){var c;if(a&&"function"==typeof a){var u=0;c=function(l,f,d){if(l&&"EAGAIN"===l.code&&u<10)return u++,t.call(e,n,r,i,o,s,c);a.apply(this,arguments)}}return t.call(e,n,r,i,o,s,c)}return n.__proto__=t,n}(e.read),e.readSync=(n=e.readSync,function(t,r,i,o,s){for(var a=0;;)try{return n.call(e,t,r,i,o,s)}catch(e){if("EAGAIN"===e.code&&a<10){a++;continue}throw e}})}},function(e,t){e.exports=require("constants")},function(e,t,n){var r=n(27).Stream;e.exports=function(e){return{ReadStream:function t(n,i){if(!(this instanceof t))return new t(n,i);r.call(this);var o=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,i=i||{};for(var s=Object.keys(i),a=0,c=s.length;a<c;a++){var u=s[a];this[u]=i[u]}if(this.encoding&&this.setEncoding(this.encoding),void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(void 0===this.end)this.end=1/0;else if("number"!=typeof this.end)throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}null===this.fd?e.open(this.path,this.flags,this.mode,(function(e,t){if(e)return o.emit("error",e),void(o.readable=!1);o.fd=t,o.emit("open",t),o._read()})):process.nextTick((function(){o._read()}))},WriteStream:function t(n,i){if(!(this instanceof t))return new t(n,i);r.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,i=i||{};for(var o=Object.keys(i),s=0,a=o.length;s<a;s++){var c=o[s];this[c]=i[c]}if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],null===this.fd&&(this._open=e.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}}},function(e,t,n){"use strict";e.exports=function(e){if(null===e||"object"!=typeof e)return e;if(e instanceof Object)var t={__proto__:e.__proto__};else t=Object.create(null);return Object.getOwnPropertyNames(e).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})),t}},function(e,t,n){"use strict";const r=n(4),i=n(0),o=n(11).mkdirsSync,s=n(62).utimesMillisSync,a=n(32);function c(e,t,n,o){if(!o.filter||o.filter(t,n))return function(e,t,n,o){const s=(o.dereference?r.statSync:r.lstatSync)(t);return s.isDirectory()?function(e,t,n,i,o){if(!t)return function(e,t,n,i){return r.mkdirSync(n),f(t,n,i),l(n,e)}(e.mode,n,i,o);if(t&&!t.isDirectory())throw new Error(`Cannot overwrite non-directory '${i}' with directory '${n}'.`);return f(n,i,o)}(s,e,t,n,o):s.isFile()||s.isCharacterDevice()||s.isBlockDevice()?function(e,t,n,i,o){return t?function(e,t,n,i){if(i.overwrite)return r.unlinkSync(n),u(e,t,n,i);if(i.errorOnExist)throw new Error(`'${n}' already exists`)}(e,n,i,o):u(e,n,i,o)}(s,e,t,n,o):s.isSymbolicLink()?function(e,t,n,o){let s=r.readlinkSync(t);if(o.dereference&&(s=i.resolve(process.cwd(),s)),e){let t;try{t=r.readlinkSync(n)}catch(e){if("EINVAL"===e.code||"UNKNOWN"===e.code)return r.symlinkSync(s,n);throw e}if(o.dereference&&(t=i.resolve(process.cwd(),t)),a.isSrcSubdir(s,t))throw new Error(`Cannot copy '${s}' to a subdirectory of itself, '${t}'.`);if(r.statSync(n).isDirectory()&&a.isSrcSubdir(t,s))throw new Error(`Cannot overwrite '${t}' with '${s}'.`);return function(e,t){return r.unlinkSync(t),r.symlinkSync(e,t)}(s,n)}return r.symlinkSync(s,n)}(e,t,n,o):void 0}(e,t,n,o)}function u(e,t,n,i){return r.copyFileSync(t,n),i.preserveTimestamps&&function(e,t,n){(function(e){return 0==(128&e)})(e)&&function(e,t){l(e,128|t)}(n,e),function(e,t){const n=r.statSync(e);s(t,n.atime,n.mtime)}(t,n)}(e.mode,t,n),l(n,e.mode)}function l(e,t){return r.chmodSync(e,t)}function f(e,t,n){r.readdirSync(e).forEach(r=>function(e,t,n,r){const o=i.join(t,e),s=i.join(n,e),{destStat:u}=a.checkPathsSync(o,s,"copy");return c(u,o,s,r)}(r,e,t,n))}e.exports=function(e,t,n){"function"==typeof n&&(n={filter:n}),(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&console.warn("fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269");const{srcStat:s,destStat:u}=a.checkPathsSync(e,t,"copy");return a.checkParentPathsSync(e,s,t,"copy"),function(e,t,n,s){if(s.filter&&!s.filter(t,n))return;const a=i.dirname(n);return r.existsSync(a)||o(a),c(e,t,n,s)}(u,e,t,n)}},function(e,t,n){"use strict";const r=n(30),i=n(0),o=n(61)("10.12.0"),s=e=>{if("win32"===process.platform&&/[<>:"|?*]/.test(e.replace(i.parse(e).root,""))){const t=new Error("Path contains invalid characters: "+e);throw t.code="EINVAL",t}},a=e=>("number"==typeof e&&(e={mode:e}),{mode:511,...e}),c=e=>{const t=new Error(`operation not permitted, mkdir '${e}'`);return t.code="EPERM",t.errno=-4048,t.path=e,t.syscall="mkdir",t};e.exports.makeDir=async(e,t)=>{if(s(e),t=a(t),o){const n=i.resolve(e);return r.mkdir(n,{mode:t.mode,recursive:!0})}const n=async e=>{try{await r.mkdir(e,t.mode)}catch(t){if("EPERM"===t.code)throw t;if("ENOENT"===t.code){if(i.dirname(e)===e)throw c(e);if(t.message.includes("null bytes"))throw t;return await n(i.dirname(e)),n(e)}try{if(!(await r.stat(e)).isDirectory())throw new Error("The path is not a directory")}catch{throw t}}};return n(i.resolve(e))},e.exports.makeDirSync=(e,t)=>{if(s(e),t=a(t),o){const n=i.resolve(e);return r.mkdirSync(n,{mode:t.mode,recursive:!0})}const n=e=>{try{r.mkdirSync(e,t.mode)}catch(t){if("EPERM"===t.code)throw t;if("ENOENT"===t.code){if(i.dirname(e)===e)throw c(e);if(t.message.includes("null bytes"))throw t;return n(i.dirname(e)),n(e)}try{if(!r.statSync(e).isDirectory())throw new Error("The path is not a directory")}catch{throw t}}};return n(i.resolve(e))}},function(e,t,n){"use strict";const r=n(4),i=n(0),o=n(11).mkdirs,s=n(18).pathExists,a=n(62).utimesMillis,c=n(32);function u(e,t,n,r,a){const c=i.dirname(n);s(c,(i,s)=>i?a(i):s?f(e,t,n,r,a):void o(c,i=>i?a(i):f(e,t,n,r,a)))}function l(e,t,n,r,i,o){Promise.resolve(i.filter(n,r)).then(s=>s?e(t,n,r,i,o):o(),e=>o(e))}function f(e,t,n,r,i){return r.filter?l(d,e,t,n,r,i):d(e,t,n,r,i)}function d(e,t,n,i,o){(i.dereference?r.stat:r.lstat)(t,(s,a)=>s?o(s):a.isDirectory()?function(e,t,n,i,o,s){return t?t&&!t.isDirectory()?s(new Error(`Cannot overwrite non-directory '${i}' with directory '${n}'.`)):g(n,i,o,s):function(e,t,n,i,o){r.mkdir(n,r=>{if(r)return o(r);g(t,n,i,t=>t?o(t):m(n,e,o))})}(e.mode,n,i,o,s)}(a,e,t,n,i,o):a.isFile()||a.isCharacterDevice()||a.isBlockDevice()?function(e,t,n,i,o,s){return t?function(e,t,n,i,o){if(!i.overwrite)return i.errorOnExist?o(new Error(`'${n}' already exists`)):o();r.unlink(n,r=>r?o(r):h(e,t,n,i,o))}(e,n,i,o,s):h(e,n,i,o,s)}(a,e,t,n,i,o):a.isSymbolicLink()?v(e,t,n,i,o):void 0)}function h(e,t,n,i,o){r.copyFile(t,n,r=>r?o(r):i.preserveTimestamps?function(e,t,n,r){return function(e){return 0==(128&e)}(e)?function(e,t,n){return m(e,128|t,n)}(n,e,i=>i?r(i):p(e,t,n,r)):p(e,t,n,r)}(e.mode,t,n,o):m(n,e.mode,o))}function p(e,t,n,i){!function(e,t,n){r.stat(e,(e,r)=>e?n(e):a(t,r.atime,r.mtime,n))}(t,n,t=>t?i(t):m(n,e,i))}function m(e,t,n){return r.chmod(e,t,n)}function g(e,t,n,i){r.readdir(e,(r,o)=>r?i(r):y(o,e,t,n,i))}function y(e,t,n,r,o){const s=e.pop();return s?function(e,t,n,r,o,s){const a=i.join(n,t),u=i.join(r,t);c.checkPaths(a,u,"copy",(t,i)=>{if(t)return s(t);const{destStat:c}=i;f(c,a,u,o,t=>t?s(t):y(e,n,r,o,s))})}(e,s,t,n,r,o):o()}function v(e,t,n,o,s){r.readlink(t,(t,a)=>t?s(t):(o.dereference&&(a=i.resolve(process.cwd(),a)),e?void r.readlink(n,(t,u)=>t?"EINVAL"===t.code||"UNKNOWN"===t.code?r.symlink(a,n,s):s(t):(o.dereference&&(u=i.resolve(process.cwd(),u)),c.isSrcSubdir(a,u)?s(new Error(`Cannot copy '${a}' to a subdirectory of itself, '${u}'.`)):e.isDirectory()&&c.isSrcSubdir(u,a)?s(new Error(`Cannot overwrite '${u}' with '${a}'.`)):function(e,t,n){r.unlink(t,i=>i?n(i):r.symlink(e,t,n))}(a,n,s))):r.symlink(a,n,s)))}e.exports=function(e,t,n,r){"function"!=typeof n||r?"function"==typeof n&&(n={filter:n}):(r=n,n={}),r=r||function(){},(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&console.warn("fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269"),c.checkPaths(e,t,"copy",(i,o)=>{if(i)return r(i);const{srcStat:s,destStat:a}=o;c.checkParentPaths(e,s,t,"copy",i=>i?r(i):n.filter?l(u,a,e,t,n,r):u(a,e,t,n,r))})}},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(4),o=n(0),s=n(11),a=n(33),c=r((function(e,t){t=t||function(){},i.readdir(e,(n,r)=>{if(n)return s.mkdirs(e,t);r=r.map(t=>o.join(e,t)),function e(){const n=r.pop();if(!n)return t();a.remove(n,n=>{if(n)return t(n);e()})}()})}));function u(e){let t;try{t=i.readdirSync(e)}catch{return s.mkdirsSync(e)}t.forEach(t=>{t=o.join(e,t),a.removeSync(t)})}e.exports={emptyDirSync:u,emptydirSync:u,emptyDir:c,emptydir:c}},function(e,t,n){"use strict";const r=n(4),i=n(0),o=n(31),s="win32"===process.platform;function a(e){["unlink","chmod","stat","lstat","rmdir","readdir"].forEach(t=>{e[t]=e[t]||r[t],e[t+="Sync"]=e[t]||r[t]}),e.maxBusyTries=e.maxBusyTries||3}function c(e,t,n){let r=0;"function"==typeof t&&(n=t,t={}),o(e,"rimraf: missing path"),o.strictEqual(typeof e,"string","rimraf: path should be a string"),o.strictEqual(typeof n,"function","rimraf: callback function required"),o(t,"rimraf: invalid options argument provided"),o.strictEqual(typeof t,"object","rimraf: options should be object"),a(t),u(e,t,(function i(o){if(o){if(("EBUSY"===o.code||"ENOTEMPTY"===o.code||"EPERM"===o.code)&&r<t.maxBusyTries)return r++,setTimeout(()=>u(e,t,i),100*r);"ENOENT"===o.code&&(o=null)}n(o)}))}function u(e,t,n){o(e),o(t),o("function"==typeof n),t.lstat(e,(r,i)=>r&&"ENOENT"===r.code?n(null):r&&"EPERM"===r.code&&s?l(e,t,r,n):i&&i.isDirectory()?d(e,t,r,n):void t.unlink(e,r=>{if(r){if("ENOENT"===r.code)return n(null);if("EPERM"===r.code)return s?l(e,t,r,n):d(e,t,r,n);if("EISDIR"===r.code)return d(e,t,r,n)}return n(r)}))}function l(e,t,n,r){o(e),o(t),o("function"==typeof r),t.chmod(e,438,i=>{i?r("ENOENT"===i.code?null:n):t.stat(e,(i,o)=>{i?r("ENOENT"===i.code?null:n):o.isDirectory()?d(e,t,n,r):t.unlink(e,r)})})}function f(e,t,n){let r;o(e),o(t);try{t.chmodSync(e,438)}catch(e){if("ENOENT"===e.code)return;throw n}try{r=t.statSync(e)}catch(e){if("ENOENT"===e.code)return;throw n}r.isDirectory()?p(e,t,n):t.unlinkSync(e)}function d(e,t,n,r){o(e),o(t),o("function"==typeof r),t.rmdir(e,s=>{!s||"ENOTEMPTY"!==s.code&&"EEXIST"!==s.code&&"EPERM"!==s.code?s&&"ENOTDIR"===s.code?r(n):r(s):function(e,t,n){o(e),o(t),o("function"==typeof n),t.readdir(e,(r,o)=>{if(r)return n(r);let s,a=o.length;if(0===a)return t.rmdir(e,n);o.forEach(r=>{c(i.join(e,r),t,r=>{if(!s)return r?n(s=r):void(0==--a&&t.rmdir(e,n))})})})}(e,t,r)})}function h(e,t){let n;a(t=t||{}),o(e,"rimraf: missing path"),o.strictEqual(typeof e,"string","rimraf: path should be a string"),o(t,"rimraf: missing options"),o.strictEqual(typeof t,"object","rimraf: options should be object");try{n=t.lstatSync(e)}catch(n){if("ENOENT"===n.code)return;"EPERM"===n.code&&s&&f(e,t,n)}try{n&&n.isDirectory()?p(e,t,null):t.unlinkSync(e)}catch(n){if("ENOENT"===n.code)return;if("EPERM"===n.code)return s?f(e,t,n):p(e,t,n);if("EISDIR"!==n.code)throw n;p(e,t,n)}}function p(e,t,n){o(e),o(t);try{t.rmdirSync(e)}catch(r){if("ENOTDIR"===r.code)throw n;if("ENOTEMPTY"===r.code||"EEXIST"===r.code||"EPERM"===r.code)!function(e,t){if(o(e),o(t),t.readdirSync(e).forEach(n=>h(i.join(e,n),t)),!s)return t.rmdirSync(e,t);{const n=Date.now();do{try{return t.rmdirSync(e,t)}catch{}}while(Date.now()-n<500)}}(e,t);else if("ENOENT"!==r.code)throw r}}e.exports=c,c.sync=h},function(e,t,n){"use strict";const r=n(126),i=n(127),o=n(128);e.exports={createFile:r.createFile,createFileSync:r.createFileSync,ensureFile:r.createFile,ensureFileSync:r.createFileSync,createLink:i.createLink,createLinkSync:i.createLinkSync,ensureLink:i.createLink,ensureLinkSync:i.createLinkSync,createSymlink:o.createSymlink,createSymlinkSync:o.createSymlinkSync,ensureSymlink:o.createSymlink,ensureSymlinkSync:o.createSymlinkSync}},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(0),o=n(4),s=n(11);e.exports={createFile:r((function(e,t){function n(){o.writeFile(e,"",e=>{if(e)return t(e);t()})}o.stat(e,(r,a)=>{if(!r&&a.isFile())return t();const c=i.dirname(e);o.stat(c,(e,r)=>{if(e)return"ENOENT"===e.code?s.mkdirs(c,e=>{if(e)return t(e);n()}):t(e);r.isDirectory()?n():o.readdir(c,e=>{if(e)return t(e)})})})})),createFileSync:function(e){let t;try{t=o.statSync(e)}catch{}if(t&&t.isFile())return;const n=i.dirname(e);try{o.statSync(n).isDirectory()||o.readdirSync(n)}catch(e){if(!e||"ENOENT"!==e.code)throw e;s.mkdirsSync(n)}o.writeFileSync(e,"")}}},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(0),o=n(4),s=n(11),a=n(18).pathExists;e.exports={createLink:r((function(e,t,n){function r(e,t){o.link(e,t,e=>{if(e)return n(e);n(null)})}a(t,(c,u)=>c?n(c):u?n(null):void o.lstat(e,o=>{if(o)return o.message=o.message.replace("lstat","ensureLink"),n(o);const c=i.dirname(t);a(c,(i,o)=>i?n(i):o?r(e,t):void s.mkdirs(c,i=>{if(i)return n(i);r(e,t)}))}))})),createLinkSync:function(e,t){if(o.existsSync(t))return;try{o.lstatSync(e)}catch(e){throw e.message=e.message.replace("lstat","ensureLink"),e}const n=i.dirname(t);return o.existsSync(n)||s.mkdirsSync(n),o.linkSync(e,t)}}},function(e,t,n){"use strict";const r=n(8).fromCallback,i=n(0),o=n(4),s=n(11),a=s.mkdirs,c=s.mkdirsSync,u=n(129),l=u.symlinkPaths,f=u.symlinkPathsSync,d=n(130),h=d.symlinkType,p=d.symlinkTypeSync,m=n(18).pathExists;e.exports={createSymlink:r((function(e,t,n,r){r="function"==typeof n?n:r,n="function"!=typeof n&&n,m(t,(s,c)=>s?r(s):c?r(null):void l(e,t,(s,c)=>{if(s)return r(s);e=c.toDst,h(c.toCwd,n,(n,s)=>{if(n)return r(n);const c=i.dirname(t);m(c,(n,i)=>n?r(n):i?o.symlink(e,t,s,r):void a(c,n=>{if(n)return r(n);o.symlink(e,t,s,r)}))})}))})),createSymlinkSync:function(e,t,n){if(o.existsSync(t))return;const r=f(e,t);e=r.toDst,n=p(r.toCwd,n);const s=i.dirname(t);return o.existsSync(s)||c(s),o.symlinkSync(e,t,n)}}},function(e,t,n){"use strict";const r=n(0),i=n(4),o=n(18).pathExists;e.exports={symlinkPaths:function(e,t,n){if(r.isAbsolute(e))return i.lstat(e,t=>t?(t.message=t.message.replace("lstat","ensureSymlink"),n(t)):n(null,{toCwd:e,toDst:e}));{const s=r.dirname(t),a=r.join(s,e);return o(a,(t,o)=>t?n(t):o?n(null,{toCwd:a,toDst:e}):i.lstat(e,t=>t?(t.message=t.message.replace("lstat","ensureSymlink"),n(t)):n(null,{toCwd:e,toDst:r.relative(s,e)})))}},symlinkPathsSync:function(e,t){let n;if(r.isAbsolute(e)){if(n=i.existsSync(e),!n)throw new Error("absolute srcpath does not exist");return{toCwd:e,toDst:e}}{const o=r.dirname(t),s=r.join(o,e);if(n=i.existsSync(s),n)return{toCwd:s,toDst:e};if(n=i.existsSync(e),!n)throw new Error("relative srcpath does not exist");return{toCwd:e,toDst:r.relative(o,e)}}}}},function(e,t,n){"use strict";const r=n(4);e.exports={symlinkType:function(e,t,n){if(n="function"==typeof t?t:n,t="function"!=typeof t&&t)return n(null,t);r.lstat(e,(e,r)=>{if(e)return n(null,"file");t=r&&r.isDirectory()?"dir":"file",n(null,t)})},symlinkTypeSync:function(e,t){let n;if(t)return t;try{n=r.lstatSync(e)}catch{return"file"}return n&&n.isDirectory()?"dir":"file"}}},function(e,t,n){"use strict";const r=n(8).fromPromise,i=n(132);i.outputJson=r(n(134)),i.outputJsonSync=n(135),i.outputJSON=i.outputJson,i.outputJSONSync=i.outputJsonSync,i.writeJSON=i.writeJson,i.writeJSONSync=i.writeJsonSync,i.readJSON=i.readJson,i.readJSONSync=i.readJsonSync,e.exports=i},function(e,t,n){"use strict";const r=n(133);e.exports={readJson:r.readFile,readJsonSync:r.readFileSync,writeJson:r.writeFile,writeJsonSync:r.writeFileSync}},function(e,t,n){let r;try{r=n(4)}catch(e){r=n(1)}const i=n(8),{stringify:o,stripBom:s}=n(39),a={readFile:i.fromPromise((async function(e,t={}){"string"==typeof t&&(t={encoding:t});const n=t.fs||r,o=!("throws"in t)||t.throws;let a,c=await i.fromCallback(n.readFile)(e,t);c=s(c);try{a=JSON.parse(c,t?t.reviver:null)}catch(t){if(o)throw t.message=`${e}: ${t.message}`,t;return null}return a})),readFileSync:function(e,t={}){"string"==typeof t&&(t={encoding:t});const n=t.fs||r,i=!("throws"in t)||t.throws;try{let r=n.readFileSync(e,t);return r=s(r),JSON.parse(r,t.reviver)}catch(t){if(i)throw t.message=`${e}: ${t.message}`,t;return null}},writeFile:i.fromPromise((async function(e,t,n={}){const s=n.fs||r,a=o(t,n);await i.fromCallback(s.writeFile)(e,a,n)})),writeFileSync:function(e,t,n={}){const i=n.fs||r,s=o(t,n);return i.writeFileSync(e,s,n)}};e.exports=a},function(e,t,n){"use strict";const{stringify:r}=n(39),{outputFile:i}=n(40);e.exports=async function(e,t,n={}){const o=r(t,n);await i(e,o,n)}},function(e,t,n){"use strict";const{stringify:r}=n(39),{outputFileSync:i}=n(40);e.exports=function(e,t,n){const o=r(t,n);i(e,o,n)}},function(e,t,n){"use strict";e.exports={moveSync:n(137)}},function(e,t,n){"use strict";const r=n(4),i=n(0),o=n(60).copySync,s=n(33).removeSync,a=n(11).mkdirpSync,c=n(32);function u(e,t,n){try{r.renameSync(e,t)}catch(r){if("EXDEV"!==r.code)throw r;return function(e,t,n){return o(e,t,{overwrite:n,errorOnExist:!0}),s(e)}(e,t,n)}}e.exports=function(e,t,n){const o=(n=n||{}).overwrite||n.clobber||!1,{srcStat:l}=c.checkPathsSync(e,t,"move");return c.checkParentPathsSync(e,l,t,"move"),a(i.dirname(t)),function(e,t,n){if(n)return s(t),u(e,t,n);if(r.existsSync(t))throw new Error("dest already exists.");return u(e,t,n)}(e,t,o)}},function(e,t,n){"use strict";const r=n(8).fromCallback;e.exports={move:r(n(139))}},function(e,t,n){"use strict";const r=n(4),i=n(0),o=n(63).copy,s=n(33).remove,a=n(11).mkdirp,c=n(18).pathExists,u=n(32);function l(e,t,n,i){r.rename(e,t,r=>r?"EXDEV"!==r.code?i(r):function(e,t,n,r){o(e,t,{overwrite:n,errorOnExist:!0},t=>t?r(t):s(e,r))}(e,t,n,i):i())}e.exports=function(e,t,n,r){"function"==typeof n&&(r=n,n={});const o=n.overwrite||n.clobber||!1;u.checkPaths(e,t,"move",(n,f)=>{if(n)return r(n);const{srcStat:d}=f;u.checkParentPaths(e,d,t,"move",n=>{if(n)return r(n);a(i.dirname(t),n=>n?r(n):function(e,t,n,r){if(n)return s(t,i=>i?r(i):l(e,t,n,r));c(t,(i,o)=>i?r(i):o?r(new Error("dest already exists.")):l(e,t,n,r))}(e,t,o,r))})})}},,function(e,t,n){"use strict";const r=n(3),i=n(64),o=n(65),s=n(152),a=n(153),c=n(21),{assertDefaultLevelFound:u,mappings:l,genLsCache:f}=n(68),{createArgsNormalizer:d,asChindings:h,final:p,stringify:m,buildSafeSonicBoom:g,buildFormatters:y,noop:v}=n(44),{version:b}=n(72),{chindingsSym:_,redactFmtSym:w,serializersSym:S,timeSym:x,timeSliceIndexSym:k,streamSym:E,stringifySym:T,stringifiersSym:R,setLevelSym:P,endSym:O,formatOptsSym:C,messageKeySym:D,nestedKeySym:M,mixinSym:N,useOnlyCustomLevelsSym:j,formattersSym:q,hooksSym:L}=c,{epochTime:F,nullTime:I}=s,{pid:A}=process,W=r.hostname(),$=i.err,H={level:"info",messageKey:"msg",nestedKey:null,enabled:!0,prettyPrint:!1,base:{pid:A,hostname:W},serializers:Object.assign(Object.create(null),{err:$}),formatters:Object.assign(Object.create(null),{bindings:e=>e,level:(e,t)=>({level:t})}),hooks:{logMethod:void 0},timestamp:F,name:void 0,redact:null,customLevels:null,levelKey:void 0,useOnlyCustomLevels:!1},z=d(H),B=Object.assign(Object.create(null),i);function U(...e){const t={},{opts:n,stream:r}=z(t,...e),{redact:i,crlf:s,serializers:c,timestamp:d,messageKey:p,nestedKey:g,base:b,name:A,level:W,customLevels:$,useLevelLabels:B,changeLevelName:U,levelKey:J,mixin:V,useOnlyCustomLevels:G,formatters:Y,hooks:Z}=n,Q=y(Y.level,Y.bindings,Y.log);!B||U||J?!U&&!J||B?(U||J)&&B&&(process.emitWarning("useLevelLabels is deprecated, use the formatters.level option instead","Warning","PINODEP001"),process.emitWarning("changeLevelName and levelKey are deprecated, use the formatters.level option instead","Warning","PINODEP002"),Q.level=function(e){return function(t,n){return{[e]:t}}}(U||J)):(process.emitWarning("changeLevelName and levelKey are deprecated, use the formatters.level option instead","Warning","PINODEP002"),Q.level=function(e){return function(t,n){return{[e]:n}}}(U||J)):(process.emitWarning("useLevelLabels is deprecated, use the formatters.level option instead","Warning","PINODEP001"),Q.level=K),c[Symbol.for("pino.*")]&&(process.emitWarning("The pino.* serializer is deprecated, use the formatters.log options instead","Warning","PINODEP003"),Q.log=c[Symbol.for("pino.*")]),Q.bindings||(Q.bindings=H.formatters.bindings),Q.level||(Q.level=H.formatters.level);const X=i?o(i,m):{},ee=i?{stringify:X[w]}:{stringify:m},te="}"+(s?"\r\n":"\n"),ne=h.bind(null,{[_]:"",[S]:c,[R]:X,[T]:m,[q]:Q});let re="";null!==b&&(re=ne(void 0===A?b:Object.assign({},b,{name:A})));const ie=d instanceof Function?d:d?F:I,oe=ie().indexOf(":")+1;if(G&&!$)throw Error("customLevels is required if useOnlyCustomLevels is set true");if(V&&"function"!=typeof V)throw Error(`Unknown mixin type "${typeof V}" - expected "function"`);u(W,$,G);const se=l($,G);return Object.assign(t,{levels:se,[j]:G,[E]:r,[x]:ie,[k]:oe,[T]:m,[R]:X,[O]:te,[C]:ee,[D]:p,[M]:g,[S]:c,[N]:V,[_]:re,[q]:Q,[L]:Z,silent:v}),Object.setPrototypeOf(t,a()),f(t),t[P](W),t}function K(e,t){return{level:e}}e.exports=U,e.exports.extreme=(e=process.stdout.fd)=>(process.emitWarning("The pino.extreme() option is deprecated and will be removed in v7. Use pino.destination({ sync: false }) instead.",{code:"extreme_deprecation"}),g({dest:e,minLength:4096,sync:!1})),e.exports.destination=(e=process.stdout.fd)=>"object"==typeof e?(e.dest=e.dest||process.stdout.fd,g(e)):g({dest:e,minLength:0,sync:!0}),e.exports.final=p,e.exports.levels=l(),e.exports.stdSerializers=B,e.exports.stdTimeFunctions=Object.assign({},s),e.exports.symbols=c,e.exports.version=b,e.exports.default=U,e.exports.pino=U},function(e,t,n){"use strict";e.exports=function e(t){if(!(t instanceof Error))return t;t[i]=void 0;const n=Object.create(s);n.type="[object Function]"===r.call(t.constructor)?t.constructor.name:t.name,n.message=t.message,n.stack=t.stack;for(const r in t)if(void 0===n[r]){const o=t[r];o instanceof Error?o.hasOwnProperty(i)||(n[r]=e(o)):n[r]=o}return delete t[i],n.raw=t,n};const{toString:r}=Object.prototype,i=Symbol("circular-ref-tag"),o=Symbol("pino-raw-err-ref"),s=Object.create({},{type:{enumerable:!0,writable:!0,value:void 0},message:{enumerable:!0,writable:!0,value:void 0},stack:{enumerable:!0,writable:!0,value:void 0},raw:{enumerable:!1,get:function(){return this[o]},set:function(e){this[o]=e}}});Object.defineProperty(s,o,{writable:!0,value:{}})},function(e,t,n){"use strict";e.exports={mapHttpRequest:function(e){return{req:o(e)}},reqSerializer:o};const r=Symbol("pino-raw-req-ref"),i=Object.create({},{id:{enumerable:!0,writable:!0,value:""},method:{enumerable:!0,writable:!0,value:""},url:{enumerable:!0,writable:!0,value:""},query:{enumerable:!0,writable:!0,value:""},params:{enumerable:!0,writable:!0,value:""},headers:{enumerable:!0,writable:!0,value:{}},remoteAddress:{enumerable:!0,writable:!0,value:""},remotePort:{enumerable:!0,writable:!0,value:""},raw:{enumerable:!1,get:function(){return this[r]},set:function(e){this[r]=e}}});function o(e){const t=e.info||e.socket,n=Object.create(i);return n.id="function"==typeof e.id?e.id():e.id||(e.info?e.info.id:void 0),n.method=e.method,e.originalUrl?(n.url=e.originalUrl,n.query=e.query,n.params=e.params):n.url=e.path||(e.url?e.url.path||e.url:void 0),n.headers=e.headers,n.remoteAddress=t&&t.remoteAddress,n.remotePort=t&&t.remotePort,n.raw=e.raw||e,n}Object.defineProperty(i,r,{writable:!0,value:{}})},function(e,t,n){"use strict";e.exports={mapHttpResponse:function(e){return{res:o(e)}},resSerializer:o};const r=Symbol("pino-raw-res-ref"),i=Object.create({},{statusCode:{enumerable:!0,writable:!0,value:0},headers:{enumerable:!0,writable:!0,value:""},raw:{enumerable:!1,get:function(){return this[r]},set:function(e){this[r]=e}}});function o(e){const t=Object.create(i);return t.statusCode=e.statusCode,t.headers=e.getHeaders?e.getHeaders():e._headers,t.raw=e,t}Object.defineProperty(i,r,{writable:!0,value:{}})},function(e,t,n){"use strict";const r=n(146),i=n(148),o=n(149),s=n(150),{groupRedact:a,nestedRedact:c}=n(66),u=n(151),l=n(42),f=r(),d=e=>e;function h(e={}){const t=Array.from(new Set(e.paths||[])),n=!("serialize"in e)||!1!==e.serialize&&"function"!=typeof e.serialize?JSON.stringify:e.serialize,r=e.remove;if(!0===r&&n!==JSON.stringify)throw Error("fast-redact – remove option may only be set when serializer is JSON.stringify");const l=!0===r?void 0:"censor"in e?e.censor:"[REDACTED]",h="function"==typeof l,p=h&&l.length>1;if(0===t.length)return n||d;f({paths:t,serialize:n,censor:l});const{wildcards:m,wcLen:g,secret:y}=i({paths:t,censor:l}),v=s({secret:y,wcLen:g}),b=!("strict"in e)||e.strict;return o({secret:y,wcLen:g,serialize:n,strict:b,isCensorFct:h,censorFctTakesPath:p},u({secret:y,censor:l,compileRestore:v,serialize:n,groupRedact:a,nestedRedact:c,wildcards:m,wcLen:g}))}d.restore=d,h.rx=l,h.validator=r,e.exports=h},function(e,t,n){"use strict";const{createContext:r,runInContext:i}=n(147);e.exports=function(e={}){const{ERR_PATHS_MUST_BE_STRINGS:t=(()=>"fast-redact - Paths must be (non-empty) strings"),ERR_INVALID_PATH:n=(e=>`fast-redact – Invalid path (${e})`)}=e;return function({paths:e}){e.forEach(e=>{if("string"!=typeof e)throw Error(t());try{if(/〇/.test(e))throw Error();const t=new Proxy({},{get:()=>t,set:()=>{throw Error()}}),n=("["===e[0]?"":".")+e.replace(/^\*/,"〇").replace(/\.\*/g,".〇").replace(/\[\*\]/g,"[〇]");if(/\n|\r|;/.test(n))throw Error();if(/\/\*/.test(n))throw Error();i(`\n          (function () {\n            'use strict'\n            o${n}\n            if ([o${n}].length !== 1) throw Error()\n          })()\n        `,r({o:t,"〇":null}),{codeGeneration:{strings:!1,wasm:!1}})}catch(t){throw Error(n(e))}})}}},function(e,t){e.exports=require("vm")},function(e,t,n){"use strict";const r=n(42);e.exports=function({paths:e}){const t=[];var n=0;const i=e.reduce((function(e,i,o){var s=i.match(r).map(e=>e.replace(/'|"|`/g,""));const a="["===i[0],c=(s=s.map(e=>"["===e[0]?e.substr(1,e.length-2):e)).indexOf("*");if(c>-1){const e=s.slice(0,c),r=e.join("."),i=s.slice(c+1,s.length);if(i.indexOf("*")>-1)throw Error("fast-redact – Only one wildcard per path is supported");const o=i.length>0;n++,t.push({before:e,beforeStr:r,after:i,nested:o})}else e[i]={path:s,val:void 0,precensored:!1,circle:"",escPath:JSON.stringify(i),leadingBracket:a};return e}),{});return{wildcards:t,wcLen:n,secret:i}}},function(e,t,n){"use strict";const r=n(42);e.exports=function({secret:e,serialize:t,wcLen:n,strict:i,isCensorFct:o,censorFctTakesPath:s},a){const c=Function("o",`\n    if (typeof o !== 'object' || o == null) {\n      ${function(e,t){return!0===e?"throw Error('fast-redact: primitives cannot be redacted')":!1===t?"return o":"return this.serialize(o)"}(i,t)}\n    }\n    const { censor, secret } = this\n    ${function(e,t,n){return Object.keys(e).map(i=>{const{escPath:o,leadingBracket:s,path:a}=e[i],c=s?1:0,u=s?"":".",l=[];for(var f;null!==(f=r.exec(i));){const[,e]=f,{index:t,input:n}=f;t>c&&l.push(n.substring(0,t-(e?0:1)))}var d=l.map(e=>`o${u}${e}`).join(" && ");0===d.length?d+=`o${u}${i} != null`:d+=` && o${u}${i} != null`;const h=`\n      switch (true) {\n        ${l.reverse().map(e=>`\n          case o${u}${e} === censor:\n            secret[${o}].circle = ${JSON.stringify(e)}\n            break\n        `).join("\n")}\n      }\n    `,p=n?"val, "+JSON.stringify(a):"val";return`\n      if (${d}) {\n        const val = o${u}${i}\n        if (val === censor) {\n          secret[${o}].precensored = true\n        } else {\n          secret[${o}].val = val\n          o${u}${i} = ${t?`censor(${p})`:"censor"}\n          ${h}\n        }\n      }\n    `}).join("\n")}(e,o,s)}\n    this.compileRestore()\n    ${function(e,t,n){return!0===e?`\n    {\n      const { wildcards, wcLen, groupRedact, nestedRedact } = this\n      for (var i = 0; i < wcLen; i++) {\n        const { before, beforeStr, after, nested } = wildcards[i]\n        if (nested === true) {\n          secret[beforeStr] = secret[beforeStr] || []\n          nestedRedact(secret[beforeStr], o, before, after, censor, ${t}, ${n})\n        } else secret[beforeStr] = groupRedact(o, before, censor, ${t}, ${n})\n      }\n    }\n  `:""}(n>0,o,s)}\n    ${function(e){return!1===e?"return o":"\n    var s = this.serialize(o)\n    this.restore(o)\n    return s\n  "}(t)}\n  `).bind(a);return!1===t&&(c.restore=e=>a.restore(e)),c}},function(e,t,n){"use strict";const{groupRestore:r,nestedRestore:i}=n(66);e.exports=function({secret:e,wcLen:t}){return function(){if(this.restore)return;const n=Object.keys(e).filter(t=>!1===e[t].precensored),o=function(e,t){return t.map(t=>{const{circle:n,escPath:r,leadingBracket:i}=e[t];return`\n      if (secret[${r}].val !== undefined) {\n        try { ${n?`o.${n} = secret[${r}].val`:`o${i?"":"."}${t} = secret[${r}].val`} } catch (e) {}\n        secret[${r}].val = undefined\n      }\n    `}).join("")}(e,n),s=t>0,a=s?{secret:e,groupRestore:r,nestedRestore:i}:{secret:e};this.restore=Function("o",function(e,t,n){return`\n    const secret = this.secret\n    ${!0===n?`\n    const keys = Object.keys(secret)\n    const len = keys.length\n    for (var i = len - 1; i >= ${t.length}; i--) {\n      const k = keys[i]\n      const o = secret[k]\n      if (o.flat === true) this.groupRestore(o)\n      else this.nestedRestore(o)\n      secret[k] = null\n    }\n  `:""}\n    ${e}\n    return o\n  `}(o,n,s)).bind(a)}}},function(e,t,n){"use strict";e.exports=function(e){const{secret:t,censor:n,compileRestore:r,serialize:i,groupRedact:o,nestedRedact:s,wildcards:a,wcLen:c}=e,u=[{secret:t,censor:n,compileRestore:r}];return!1!==i&&u.push({serialize:i}),c>0&&u.push({groupRedact:o,nestedRedact:s,wildcards:a,wcLen:c}),Object.assign(...u)}},function(e,t,n){"use strict";e.exports={nullTime:()=>"",epochTime:()=>',"time":'+Date.now(),unixTime:()=>',"time":'+Math.round(Date.now()/1e3),isoTime:()=>`,"time":"${new Date(Date.now()).toISOString()}"`}},function(e,t,n){"use strict";const{EventEmitter:r}=n(23),i=n(67),o=n(43),s=n(155),{lsCacheSym:a,levelValSym:c,setLevelSym:u,getLevelSym:l,chindingsSym:f,parsedChindingsSym:d,mixinSym:h,asJsonSym:p,writeSym:m,timeSym:g,timeSliceIndexSym:y,streamSym:v,serializersSym:b,formattersSym:_,useOnlyCustomLevelsSym:w,needsMetadataGsym:S,redactFmtSym:x,stringifySym:k,formatOptsSym:E,stringifiersSym:T}=n(21),{getLevel:R,setLevel:P,isLevelEnabled:O,mappings:C,initialLsCache:D,genLsCache:M,assertNoLevelCollisions:N}=n(68),{asChindings:j,asJson:q,buildFormatters:L,stringify:F}=n(44),{version:I}=n(72),A=n(65),W={constructor:class{},child:function(e,t){if(!e)throw Error("missing bindings for child Pino");t=t||{};const n=this[b],r=this[_],i=Object.create(this);if(!0===e.hasOwnProperty("serializers")&&(s.emit("PINODEP004"),t.serializers=e.serializers),!0===e.hasOwnProperty("formatters")&&(s.emit("PINODEP005"),t.formatters=e.formatters),!0===e.hasOwnProperty("customLevels")&&(s.emit("PINODEP006"),t.customLevels=e.customLevels),!0===e.hasOwnProperty("level")&&(s.emit("PINODEP007"),t.level=e.level),!0===t.hasOwnProperty("serializers")){i[b]=Object.create(null);for(const e in n)i[b][e]=n[e];const e=Object.getOwnPropertySymbols(n);for(var o=0;o<e.length;o++){const t=e[o];i[b][t]=n[t]}for(const e in t.serializers)i[b][e]=t.serializers[e];const r=Object.getOwnPropertySymbols(t.serializers);for(var a=0;a<r.length;a++){const e=r[a];i[b][e]=t.serializers[e]}}else i[b]=n;if(t.hasOwnProperty("formatters")){const{level:e,bindings:n,log:o}=t.formatters;i[_]=L(e||r.level,n||$,o||r.log)}else i[_]=L(r.level,$,r.log);if(!0===t.hasOwnProperty("customLevels")&&(N(this.levels,t.customLevels),i.levels=C(t.customLevels,i[w]),M(i)),"object"==typeof t.redact&&null!==t.redact||Array.isArray(t.redact)){i.redact=t.redact;const e=A(i.redact,F),n={stringify:e[x]};i[k]=F,i[T]=e,i[E]=n}i[f]=j(i,e);const c=t.level||this.level;return i[u](c),i},bindings:function(){const e=`{${this[f].substr(1)}}`,t=JSON.parse(e);return delete t.pid,delete t.hostname,t},setBindings:function(e){const t=j(this,e);this[f]=t,delete this[d]},flush:function(){const e=this[v];"flush"in e&&e.flush()},isLevelEnabled:O,version:I,get level(){return this[l]()},set level(e){this[u](e)},get levelVal(){return this[c]},set levelVal(e){throw Error("levelVal is read-only")},[a]:D,[m]:function(e,t,n){const r=this[g](),s=this[h],a=e instanceof Error;let c;null==e?c=s?s({}):{}:(c=Object.assign(s?s(e):{},e),!t&&a&&(t=e.message),a&&(c.stack=e.stack,c.type||(c.type="Error")));const u=this[p](c,t,n,r),l=this[v];!0===l[S]&&(l.lastLevel=n,l.lastObj=c,l.lastMsg=t,l.lastTime=r.slice(this[y]),l.lastLogger=this),l instanceof i?l.write(u):l.write(o(u))},[p]:q,[l]:R,[u]:P};Object.setPrototypeOf(W,r.prototype),e.exports=function(){return Object.create(W)};const $=e=>e},function(e,t,n){"use strict";if("undefined"!=typeof SharedArrayBuffer&&"undefined"!=typeof Atomics){const t=new Int32Array(new SharedArrayBuffer(4));function n(e){if(!1==(e>0&&e<1/0)){if("number"!=typeof e&&"bigint"!=typeof e)throw TypeError("sleep: ms must be a number");throw RangeError("sleep: ms must be a number that is greater than 0 but less than Infinity")}Atomics.wait(t,0,0,Number(e))}e.exports=n}else{e.exports=function(e){if(!1==(e>0&&e<1/0)){if("number"!=typeof e&&"bigint"!=typeof e)throw TypeError("sleep: ms must be a number");throw RangeError("sleep: ms must be a number that is greater than 0 but less than Infinity")}const t=Date.now()+Number(e);for(;t>Date.now(););}}},function(e,t,n){"use strict";const r=n(156)();e.exports=r;const i="PinoWarning";r.create(i,"PINODEP004","bindings.serializers is deprecated, use options.serializers option instead"),r.create(i,"PINODEP005","bindings.formatters is deprecated, use options.formatters option instead"),r.create(i,"PINODEP006","bindings.customLevels is deprecated, use options.customLevels option instead"),r.create(i,"PINODEP007","bindings.level is deprecated, use options.level option instead")},function(e,t,n){"use strict";const{format:r}=n(6);e.exports=function(){const e={},t=new Map;return{create:function(n,i,o){if(!n)throw new Error("Fastify warning name must not be empty");if(!i)throw new Error("Fastify warning code must not be empty");if(!o)throw new Error("Fastify warning message must not be empty");if(i=i.toUpperCase(),void 0!==e[i])throw new Error(`The code '${i}' already exist`);return t.set(i,!1),e[i]=function(e,t,s){let a;return a=e&&t&&s?r(o,e,t,s):e&&t?r(o,e,t):e?r(o,e):o,{code:i,name:n,message:a}},e[i]},emit:function(n,r,i,o){if(void 0===e[n])throw new Error(`The code '${n}' does not exist`);if(!0===t.get(n))return;t.set(n,!0);const s=e[n](r,i,o);process.emitWarning(s.message,s.name,s.code)},emitted:t}}},function(e,t,n){"use strict";function r(e){try{return JSON.stringify(e)}catch(e){return'"[Circular]"'}}e.exports=function(e,t,n){var i=n&&n.stringify||r;if("object"==typeof e&&null!==e){var o=t.length+1;if(1===o)return e;var s=new Array(o);s[0]=i(e);for(var a=1;a<o;a++)s[a]=i(t[a]);return s.join(" ")}if("string"!=typeof e)return e;var c=t.length;if(0===c)return e;for(var u="",l=0,f=-1,d=e&&e.length||0,h=0;h<d;){if(37===e.charCodeAt(h)&&h+1<d){switch(f=f>-1?f:0,e.charCodeAt(h+1)){case 100:case 102:if(l>=c)break;if(f<h&&(u+=e.slice(f,h)),null==t[l])break;u+=Number(t[l]),f=h+=2;break;case 105:if(l>=c)break;if(f<h&&(u+=e.slice(f,h)),null==t[l])break;u+=Math.floor(Number(t[l])),f=h+=2;break;case 79:case 111:case 106:if(l>=c)break;if(f<h&&(u+=e.slice(f,h)),void 0===t[l])break;var p=typeof t[l];if("string"===p){u+="'"+t[l]+"'",f=h+2,h++;break}if("function"===p){u+=t[l].name||"<anonymous>",f=h+2,h++;break}u+=i(t[l]),f=h+2,h++;break;case 115:if(l>=c)break;f<h&&(u+=e.slice(f,h)),u+=String(t[l]),f=h+2,h++;break;case 37:f<h&&(u+=e.slice(f,h)),u+="%",f=h+2,h++,l--}++l}++h}return-1===f?e:(f<d&&(u+=e.slice(f)),u)}},function(e,t){e.exports=i,i.default=i,i.stable=s,i.stableStringify=s;var n=[],r=[];function i(e,t,i){var o;!function e(t,i,o,s){var a;if("object"==typeof t&&null!==t){for(a=0;a<o.length;a++)if(o[a]===t){var c=Object.getOwnPropertyDescriptor(s,i);return void(void 0!==c.get?c.configurable?(Object.defineProperty(s,i,{value:"[Circular]"}),n.push([s,i,t,c])):r.push([t,i]):(s[i]="[Circular]",n.push([s,i,t])))}if(o.push(t),Array.isArray(t))for(a=0;a<t.length;a++)e(t[a],a,o,t);else{var u=Object.keys(t);for(a=0;a<u.length;a++){var l=u[a];e(t[l],l,o,t)}}o.pop()}}(e,"",[],void 0);try{o=0===r.length?JSON.stringify(e,t,i):JSON.stringify(e,c(t),i)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==n.length;){var s=n.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return o}function o(e,t){return e<t?-1:e>t?1:0}function s(e,t,i){var o,s=a(e,"",[],void 0)||e;try{o=0===r.length?JSON.stringify(s,t,i):JSON.stringify(s,c(t),i)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==n.length;){var u=n.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return o}function a(e,t,i,s){var c;if("object"==typeof e&&null!==e){for(c=0;c<i.length;c++)if(i[c]===e){var u=Object.getOwnPropertyDescriptor(s,t);return void(void 0!==u.get?u.configurable?(Object.defineProperty(s,t,{value:"[Circular]"}),n.push([s,t,e,u])):r.push([e,t]):(s[t]="[Circular]",n.push([s,t,e])))}try{if("function"==typeof e.toJSON)return}catch(e){return}if(i.push(e),Array.isArray(e))for(c=0;c<e.length;c++)a(e[c],c,i,e);else{var l={},f=Object.keys(e).sort(o);for(c=0;c<f.length;c++){var d=f[c];a(e[d],d,i,e),l[d]=e[d]}if(void 0===s)return l;n.push([s,t,e]),s[t]=l}i.pop()}}function c(e){return e=void 0!==e?e:function(e,t){return t},function(t,n){if(r.length>0)for(var i=0;i<r.length;i++){var o=r[i];if(o[1]===t&&o[0]===n){n="[Circular]",r.splice(i,1);break}}return e.call(this,t,n)}}},function(e,t,n){"use strict";const r=n(69),i=n(170),o=n(71),{ERROR_LIKE_KEYS:s,MESSAGE_KEY:a,TIMESTAMP_KEY:c}=n(45),{isObject:u,prettifyErrorLog:l,prettifyLevel:f,prettifyMessage:d,prettifyMetadata:h,prettifyObject:p,prettifyTime:m,filterLog:g}=n(171),y=n(174),v={colorize:r.supportsColor,crlf:!1,errorLikeObjectKeys:s,errorProps:"",levelFirst:!1,messageKey:a,messageFormat:!1,timestampKey:c,translateTime:!1,useMetadata:!1,outputStream:process.stdout,customPrettifiers:{},hideObject:!1,singleLine:!1};e.exports=function(e){const t=Object.assign({},v,e),n=t.crlf?"\r\n":"\n",r="    ",s=t.messageKey,a=t.levelKey,c=t.levelLabel,b=t.messageFormat,_=t.timestampKey,w=t.errorLikeObjectKeys,S=t.errorProps.split(","),x=t.customPrettifiers,k=t.ignore?new Set(t.ignore.split(",")):void 0,E=t.hideObject,T=t.singleLine,R=o(t.colorize),P=t.search;return function(e){let o;if(u(e))o=e;else{const t=(e=>{try{return{value:y.parse(e,{protoAction:"remove"})}}catch(e){return{err:e}}})(e);if(t.err||!u(t.value))return e+n;o=t.value}if(P&&!i.search(o,P))return;const v=d({log:o,messageKey:s,colorizer:R,messageFormat:b,levelLabel:c});k&&(o=g(o,k));const O=f({log:o,colorizer:R,levelKey:a}),C=h({log:o}),D=m({log:o,translateFormat:t.translateTime,timestampKey:_});let M="";if(t.levelFirst&&O&&(M=""+O),D&&""===M?M=""+D:D&&(M=`${M} ${D}`),!t.levelFirst&&O&&(M=M.length>0?`${M} ${O}`:O),C&&(M=M.length>0?`${M} ${C}:`:C),!1===M.endsWith(":")&&""!==M&&(M+=":"),v&&(M=M.length>0?`${M} ${v}`:v),M.length>0&&!T&&(M+=n),"Error"===o.type&&o.stack){M+=l({log:o,errorLikeKeys:w,errorProperties:S,ident:r,eol:n})}else if(!E){const e=[s,a,_].filter(e=>"string"==typeof o[e]||"number"==typeof o[e]),t=p({input:o,skipKeys:e,customPrettifiers:x,errorLikeKeys:w,eol:n,ident:r,singleLine:T,colorizer:R});T&&!/^\s$/.test(t)&&(M+=" "),M+=t}return M}}},function(e,t,n){"use strict";(function(e){const t=(e,t)=>(...n)=>`[${e(...n)+t}m`,r=(e,t)=>(...n)=>{const r=e(...n);return`[${38+t};5;${r}m`},i=(e,t)=>(...n)=>{const r=e(...n);return`[${38+t};2;${r[0]};${r[1]};${r[2]}m`},o=e=>e,s=(e,t,n)=>[e,t,n],a=(e,t,n)=>{Object.defineProperty(e,t,{get:()=>{const r=n();return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0}),r},enumerable:!0,configurable:!0})};let c;const u=(e,t,r,i)=>{void 0===c&&(c=n(162));const o=i?10:0,s={};for(const[n,i]of Object.entries(c)){const a="ansi16"===n?"ansi":n;n===t?s[a]=e(r,o):"object"==typeof i&&(s[a]=e(i[t],o))}return s};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,i]of Object.entries(r))n[t]={open:`[${i[0]}m`,close:`[${i[1]}m`},r[t]=n[t],e.set(i[0],i[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",a(n.color,"ansi",()=>u(t,"ansi16",o,!1)),a(n.color,"ansi256",()=>u(r,"ansi256",o,!1)),a(n.color,"ansi16m",()=>u(i,"rgb",s,!1)),a(n.bgColor,"ansi",()=>u(t,"ansi16",o,!0)),a(n.bgColor,"ansi256",()=>u(r,"ansi256",o,!0)),a(n.bgColor,"ansi16m",()=>u(i,"rgb",s,!0)),n}})}).call(this,n(161)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){const r=n(70),i=n(164),o={};Object.keys(r).forEach(e=>{o[e]={},Object.defineProperty(o[e],"channels",{value:r[e].channels}),Object.defineProperty(o[e],"labels",{value:r[e].labels});const t=i(e);Object.keys(t).forEach(n=>{const r=t[n];o[e][n]=function(e){const t=function(...t){const n=t[0];if(null==n)return n;n.length>1&&(t=n);const r=e(t);if("object"==typeof r)for(let e=r.length,t=0;t<e;t++)r[t]=Math.round(r[t]);return r};return"conversion"in e&&(t.conversion=e.conversion),t}(r),o[e][n].raw=function(e){const t=function(...t){const n=t[0];return null==n?n:(n.length>1&&(t=n),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)})}),e.exports=o},function(e,t,n){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},function(e,t,n){const r=n(70);function i(e){const t=function(){const e={},t=Object.keys(r);for(let n=t.length,r=0;r<n;r++)e[t[r]]={distance:-1,parent:null};return e}(),n=[e];for(t[e].distance=0;n.length;){const e=n.pop(),i=Object.keys(r[e]);for(let r=i.length,o=0;o<r;o++){const r=i[o],s=t[r];-1===s.distance&&(s.distance=t[e].distance+1,s.parent=e,n.unshift(r))}}return t}function o(e,t){return function(n){return t(e(n))}}function s(e,t){const n=[t[e].parent,e];let i=r[t[e].parent][e],s=t[e].parent;for(;t[s].parent;)n.unshift(t[s].parent),i=o(r[t[s].parent][s],i),s=t[s].parent;return i.conversion=n,i}e.exports=function(e){const t=i(e),n={},r=Object.keys(t);for(let e=r.length,i=0;i<e;i++){const e=r[i];null!==t[e].parent&&(n[e]=s(e,t))}return n}},function(e,t,n){"use strict";const r=n(3),i=n(166),o=n(167),{env:s}=process;let a;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function u(e,t){if(0===a)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===a)return 0;const n=a||0;if("dumb"===s.TERM)return n;if("win32"===process.platform){const e=r.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in s)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in s)||"codeship"===s.CI_NAME?1:n;if("TEAMCITY_VERSION"in s)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(s.TEAMCITY_VERSION)?1:0;if("truecolor"===s.COLORTERM)return 3;if("TERM_PROGRAM"in s){const e=parseInt((s.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(s.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(s.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(s.TERM)||"COLORTERM"in s?1:n}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?a=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(a=1),"FORCE_COLOR"in s&&(a="true"===s.FORCE_COLOR?1:"false"===s.FORCE_COLOR?0:0===s.FORCE_COLOR.length?1:Math.min(parseInt(s.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(u(e,e&&e.isTTY))},stdout:c(u(!0,i.isatty(1))),stderr:c(u(!0,i.isatty(2)))}},function(e,t){e.exports=require("tty")},function(e,t,n){"use strict";e.exports=(e,t=process.argv)=>{const n=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(n+e),i=t.indexOf("--");return-1!==r&&(-1===i||r<i)}},function(e,t,n){"use strict";e.exports={stringReplaceAll:(e,t,n)=>{let r=e.indexOf(t);if(-1===r)return e;const i=t.length;let o=0,s="";do{s+=e.substr(o,r-o)+t+n,o=r+i,r=e.indexOf(t,o)}while(-1!==r);return s+=e.substr(o),s},stringEncaseCRLFWithFirstIndex:(e,t,n,r)=>{let i=0,o="";do{const s="\r"===e[r-1];o+=e.substr(i,(s?r-1:r)-i)+t+(s?"\r\n":"\n")+n,i=r+1,r=e.indexOf("\n",i)}while(-1!==r);return o+=e.substr(i),o}}},function(e,t,n){"use strict";const r=/(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,i=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,o=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,s=/\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi,a=new Map([["n","\n"],["r","\r"],["t","\t"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e",""],["a",""]]);function c(e){const t="u"===e[0],n="{"===e[1];return t&&!n&&5===e.length||"x"===e[0]&&3===e.length?String.fromCharCode(parseInt(e.slice(1),16)):t&&n?String.fromCodePoint(parseInt(e.slice(2,-1),16)):a.get(e)||e}function u(e,t){const n=[],r=t.trim().split(/\s*,\s*/g);let i;for(const t of r){const r=Number(t);if(Number.isNaN(r)){if(!(i=t.match(o)))throw new Error(`Invalid Chalk template style argument: ${t} (in style '${e}')`);n.push(i[2].replace(s,(e,t,n)=>t?c(t):n))}else n.push(r)}return n}function l(e){i.lastIndex=0;const t=[];let n;for(;null!==(n=i.exec(e));){const e=n[1];if(n[2]){const r=u(e,n[2]);t.push([e].concat(r))}else t.push([e])}return t}function f(e,t){const n={};for(const e of t)for(const t of e.styles)n[t[0]]=e.inverse?null:t.slice(1);let r=e;for(const[e,t]of Object.entries(n))if(Array.isArray(t)){if(!(e in r))throw new Error("Unknown Chalk style: "+e);r=t.length>0?r[e](...t):r[e]}return r}e.exports=(e,t)=>{const n=[],i=[];let o=[];if(t.replace(r,(t,r,s,a,u,d)=>{if(r)o.push(c(r));else if(a){const t=o.join("");o=[],i.push(0===n.length?t:f(e,n)(t)),n.push({inverse:s,styles:l(a)})}else if(u){if(0===n.length)throw new Error("Found extraneous } in Chalk template literal");i.push(f(e,n)(o.join(""))),o=[],n.pop()}else o.push(d)}),i.push(o.join("")),n.length>0){const e=`Chalk template literal is missing ${n.length} closing bracket${1===n.length?"":"s"} (\`}\`)`;throw new Error(e)}return i.join("")}},function(e,t,n){!function(e){"use strict";function t(e){return null!==e&&"[object Array]"===Object.prototype.toString.call(e)}function n(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)}function r(e,i){if(e===i)return!0;if(Object.prototype.toString.call(e)!==Object.prototype.toString.call(i))return!1;if(!0===t(e)){if(e.length!==i.length)return!1;for(var o=0;o<e.length;o++)if(!1===r(e[o],i[o]))return!1;return!0}if(!0===n(e)){var s={};for(var a in e)if(hasOwnProperty.call(e,a)){if(!1===r(e[a],i[a]))return!1;s[a]=!0}for(var c in i)if(hasOwnProperty.call(i,c)&&!0!==s[c])return!1;return!0}return!1}function i(e){if(""===e||!1===e||null===e)return!0;if(t(e)&&0===e.length)return!0;if(n(e)){for(var r in e)if(e.hasOwnProperty(r))return!1;return!0}return!1}var o;o="function"==typeof String.prototype.trimLeft?function(e){return e.trimLeft()}:function(e){return e.match(/^\s*(.*)/)[1]};var s=0,a=2,c="UnquotedIdentifier",u="QuotedIdentifier",l="Rbracket",f="Rparen",d="Comma",h="Colon",p="Rbrace",m="Number",g="Current",y="Expref",v="Pipe",b="EQ",_="GT",w="LT",S="GTE",x="LTE",k="NE",E="Flatten",T="Star",R="Filter",P="Dot",O="Lbrace",C="Lbracket",D="Lparen",M="Literal",N={".":P,"*":T,",":d,":":h,"{":O,"}":p,"]":l,"(":D,")":f,"@":g},j={"<":!0,">":!0,"=":!0,"!":!0},q={" ":!0,"\t":!0,"\n":!0};function L(e){return e>="0"&&e<="9"||"-"===e}function F(){}F.prototype={tokenize:function(e){var t,n,r,i,o=[];for(this._current=0;this._current<e.length;)if((i=e[this._current])>="a"&&i<="z"||i>="A"&&i<="Z"||"_"===i)t=this._current,n=this._consumeUnquotedIdentifier(e),o.push({type:c,value:n,start:t});else if(void 0!==N[e[this._current]])o.push({type:N[e[this._current]],value:e[this._current],start:this._current}),this._current++;else if(L(e[this._current]))r=this._consumeNumber(e),o.push(r);else if("["===e[this._current])r=this._consumeLBracket(e),o.push(r);else if('"'===e[this._current])t=this._current,n=this._consumeQuotedIdentifier(e),o.push({type:u,value:n,start:t});else if("'"===e[this._current])t=this._current,n=this._consumeRawStringLiteral(e),o.push({type:M,value:n,start:t});else if("`"===e[this._current]){t=this._current;var s=this._consumeLiteral(e);o.push({type:M,value:s,start:t})}else if(void 0!==j[e[this._current]])o.push(this._consumeOperator(e));else if(void 0!==q[e[this._current]])this._current++;else if("&"===e[this._current])t=this._current,this._current++,"&"===e[this._current]?(this._current++,o.push({type:"And",value:"&&",start:t})):o.push({type:y,value:"&",start:t});else{if("|"!==e[this._current]){var a=new Error("Unknown character:"+e[this._current]);throw a.name="LexerError",a}t=this._current,this._current++,"|"===e[this._current]?(this._current++,o.push({type:"Or",value:"||",start:t})):o.push({type:v,value:"|",start:t})}return o},_consumeUnquotedIdentifier:function(e){var t,n=this._current;for(this._current++;this._current<e.length&&((t=e[this._current])>="a"&&t<="z"||t>="A"&&t<="Z"||t>="0"&&t<="9"||"_"===t);)this._current++;return e.slice(n,this._current)},_consumeQuotedIdentifier:function(e){var t=this._current;this._current++;for(var n=e.length;'"'!==e[this._current]&&this._current<n;){var r=this._current;"\\"!==e[r]||"\\"!==e[r+1]&&'"'!==e[r+1]?r++:r+=2,this._current=r}return this._current++,JSON.parse(e.slice(t,this._current))},_consumeRawStringLiteral:function(e){var t=this._current;this._current++;for(var n=e.length;"'"!==e[this._current]&&this._current<n;){var r=this._current;"\\"!==e[r]||"\\"!==e[r+1]&&"'"!==e[r+1]?r++:r+=2,this._current=r}return this._current++,e.slice(t+1,this._current-1).replace("\\'","'")},_consumeNumber:function(e){var t=this._current;this._current++;for(var n=e.length;L(e[this._current])&&this._current<n;)this._current++;var r=parseInt(e.slice(t,this._current));return{type:m,value:r,start:t}},_consumeLBracket:function(e){var t=this._current;return this._current++,"?"===e[this._current]?(this._current++,{type:R,value:"[?",start:t}):"]"===e[this._current]?(this._current++,{type:E,value:"[]",start:t}):{type:C,value:"[",start:t}},_consumeOperator:function(e){var t=this._current,n=e[t];return this._current++,"!"===n?"="===e[this._current]?(this._current++,{type:k,value:"!=",start:t}):{type:"Not",value:"!",start:t}:"<"===n?"="===e[this._current]?(this._current++,{type:x,value:"<=",start:t}):{type:w,value:"<",start:t}:">"===n?"="===e[this._current]?(this._current++,{type:S,value:">=",start:t}):{type:_,value:">",start:t}:"="===n&&"="===e[this._current]?(this._current++,{type:b,value:"==",start:t}):void 0},_consumeLiteral:function(e){this._current++;for(var t,n=this._current,r=e.length;"`"!==e[this._current]&&this._current<r;){var i=this._current;"\\"!==e[i]||"\\"!==e[i+1]&&"`"!==e[i+1]?i++:i+=2,this._current=i}var s=o(e.slice(n,this._current));return s=s.replace("\\`","`"),t=this._looksLikeJSON(s)?JSON.parse(s):JSON.parse('"'+s+'"'),this._current++,t},_looksLikeJSON:function(e){if(""===e)return!1;if('[{"'.indexOf(e[0])>=0)return!0;if(["true","false","null"].indexOf(e)>=0)return!0;if(!("-0123456789".indexOf(e[0])>=0))return!1;try{return JSON.parse(e),!0}catch(e){return!1}}};var I={};function A(){}function W(e){this.runtime=e}function $(e){this._interpreter=e,this.functionTable={abs:{_func:this._functionAbs,_signature:[{types:[s]}]},avg:{_func:this._functionAvg,_signature:[{types:[8]}]},ceil:{_func:this._functionCeil,_signature:[{types:[s]}]},contains:{_func:this._functionContains,_signature:[{types:[a,3]},{types:[1]}]},ends_with:{_func:this._functionEndsWith,_signature:[{types:[a]},{types:[a]}]},floor:{_func:this._functionFloor,_signature:[{types:[s]}]},length:{_func:this._functionLength,_signature:[{types:[a,3,4]}]},map:{_func:this._functionMap,_signature:[{types:[6]},{types:[3]}]},max:{_func:this._functionMax,_signature:[{types:[8,9]}]},merge:{_func:this._functionMerge,_signature:[{types:[4],variadic:!0}]},max_by:{_func:this._functionMaxBy,_signature:[{types:[3]},{types:[6]}]},sum:{_func:this._functionSum,_signature:[{types:[8]}]},starts_with:{_func:this._functionStartsWith,_signature:[{types:[a]},{types:[a]}]},min:{_func:this._functionMin,_signature:[{types:[8,9]}]},min_by:{_func:this._functionMinBy,_signature:[{types:[3]},{types:[6]}]},type:{_func:this._functionType,_signature:[{types:[1]}]},keys:{_func:this._functionKeys,_signature:[{types:[4]}]},values:{_func:this._functionValues,_signature:[{types:[4]}]},sort:{_func:this._functionSort,_signature:[{types:[9,8]}]},sort_by:{_func:this._functionSortBy,_signature:[{types:[3]},{types:[6]}]},join:{_func:this._functionJoin,_signature:[{types:[a]},{types:[9]}]},reverse:{_func:this._functionReverse,_signature:[{types:[a,3]}]},to_array:{_func:this._functionToArray,_signature:[{types:[1]}]},to_string:{_func:this._functionToString,_signature:[{types:[1]}]},to_number:{_func:this._functionToNumber,_signature:[{types:[1]}]},not_null:{_func:this._functionNotNull,_signature:[{types:[1],variadic:!0}]}}}I.EOF=0,I.UnquotedIdentifier=0,I.QuotedIdentifier=0,I.Rbracket=0,I.Rparen=0,I.Comma=0,I.Rbrace=0,I.Number=0,I.Current=0,I.Expref=0,I.Pipe=1,I.Or=2,I.And=3,I.EQ=5,I.GT=5,I.LT=5,I.GTE=5,I.LTE=5,I.NE=5,I.Flatten=9,I.Star=20,I.Filter=21,I.Dot=40,I.Not=45,I.Lbrace=50,I.Lbracket=55,I.Lparen=60,A.prototype={parse:function(e){this._loadTokens(e),this.index=0;var t=this.expression(0);if("EOF"!==this._lookahead(0)){var n=this._lookaheadToken(0),r=new Error("Unexpected token type: "+n.type+", value: "+n.value);throw r.name="ParserError",r}return t},_loadTokens:function(e){var t=(new F).tokenize(e);t.push({type:"EOF",value:"",start:e.length}),this.tokens=t},expression:function(e){var t=this._lookaheadToken(0);this._advance();for(var n=this.nud(t),r=this._lookahead(0);e<I[r];)this._advance(),n=this.led(r,n),r=this._lookahead(0);return n},_lookahead:function(e){return this.tokens[this.index+e].type},_lookaheadToken:function(e){return this.tokens[this.index+e]},_advance:function(){this.index++},nud:function(e){var t,n;switch(e.type){case M:return{type:"Literal",value:e.value};case c:return{type:"Field",name:e.value};case u:var r={type:"Field",name:e.value};if(this._lookahead(0)===D)throw new Error("Quoted identifier not allowed for function names.");return r;case"Not":return{type:"NotExpression",children:[t=this.expression(I.Not)]};case T:return t=null,{type:"ValueProjection",children:[{type:"Identity"},t=this._lookahead(0)===l?{type:"Identity"}:this._parseProjectionRHS(I.Star)]};case R:return this.led(e.type,{type:"Identity"});case O:return this._parseMultiselectHash();case E:return{type:"Projection",children:[{type:E,children:[{type:"Identity"}]},t=this._parseProjectionRHS(I.Flatten)]};case C:return this._lookahead(0)===m||this._lookahead(0)===h?(t=this._parseIndexExpression(),this._projectIfSlice({type:"Identity"},t)):this._lookahead(0)===T&&this._lookahead(1)===l?(this._advance(),this._advance(),{type:"Projection",children:[{type:"Identity"},t=this._parseProjectionRHS(I.Star)]}):this._parseMultiselectList();case g:return{type:g};case y:return{type:"ExpressionReference",children:[n=this.expression(I.Expref)]};case D:for(var i=[];this._lookahead(0)!==f;)this._lookahead(0)===g?(n={type:g},this._advance()):n=this.expression(0),i.push(n);return this._match(f),i[0];default:this._errorToken(e)}},led:function(e,t){var n;switch(e){case P:var r=I.Dot;return this._lookahead(0)!==T?{type:"Subexpression",children:[t,n=this._parseDotRHS(r)]}:(this._advance(),{type:"ValueProjection",children:[t,n=this._parseProjectionRHS(r)]});case v:return n=this.expression(I.Pipe),{type:v,children:[t,n]};case"Or":return{type:"OrExpression",children:[t,n=this.expression(I.Or)]};case"And":return{type:"AndExpression",children:[t,n=this.expression(I.And)]};case D:for(var i,o=t.name,s=[];this._lookahead(0)!==f;)this._lookahead(0)===g?(i={type:g},this._advance()):i=this.expression(0),this._lookahead(0)===d&&this._match(d),s.push(i);return this._match(f),{type:"Function",name:o,children:s};case R:var a=this.expression(0);return this._match(l),{type:"FilterProjection",children:[t,n=this._lookahead(0)===E?{type:"Identity"}:this._parseProjectionRHS(I.Filter),a]};case E:return{type:"Projection",children:[{type:E,children:[t]},this._parseProjectionRHS(I.Flatten)]};case b:case k:case _:case S:case w:case x:return this._parseComparator(t,e);case C:var c=this._lookaheadToken(0);return c.type===m||c.type===h?(n=this._parseIndexExpression(),this._projectIfSlice(t,n)):(this._match(T),this._match(l),{type:"Projection",children:[t,n=this._parseProjectionRHS(I.Star)]});default:this._errorToken(this._lookaheadToken(0))}},_match:function(e){if(this._lookahead(0)!==e){var t=this._lookaheadToken(0),n=new Error("Expected "+e+", got: "+t.type);throw n.name="ParserError",n}this._advance()},_errorToken:function(e){var t=new Error("Invalid token ("+e.type+'): "'+e.value+'"');throw t.name="ParserError",t},_parseIndexExpression:function(){if(this._lookahead(0)===h||this._lookahead(1)===h)return this._parseSliceExpression();var e={type:"Index",value:this._lookaheadToken(0).value};return this._advance(),this._match(l),e},_projectIfSlice:function(e,t){var n={type:"IndexExpression",children:[e,t]};return"Slice"===t.type?{type:"Projection",children:[n,this._parseProjectionRHS(I.Star)]}:n},_parseSliceExpression:function(){for(var e=[null,null,null],t=0,n=this._lookahead(0);n!==l&&t<3;){if(n===h)t++,this._advance();else{if(n!==m){var r=this._lookahead(0),i=new Error("Syntax error, unexpected token: "+r.value+"("+r.type+")");throw i.name="Parsererror",i}e[t]=this._lookaheadToken(0).value,this._advance()}n=this._lookahead(0)}return this._match(l),{type:"Slice",children:e}},_parseComparator:function(e,t){return{type:"Comparator",name:t,children:[e,this.expression(I[t])]}},_parseDotRHS:function(e){var t=this._lookahead(0);return[c,u,T].indexOf(t)>=0?this.expression(e):t===C?(this._match(C),this._parseMultiselectList()):t===O?(this._match(O),this._parseMultiselectHash()):void 0},_parseProjectionRHS:function(e){var t;if(I[this._lookahead(0)]<10)t={type:"Identity"};else if(this._lookahead(0)===C)t=this.expression(e);else if(this._lookahead(0)===R)t=this.expression(e);else{if(this._lookahead(0)!==P){var n=this._lookaheadToken(0),r=new Error("Sytanx error, unexpected token: "+n.value+"("+n.type+")");throw r.name="ParserError",r}this._match(P),t=this._parseDotRHS(e)}return t},_parseMultiselectList:function(){for(var e=[];this._lookahead(0)!==l;){var t=this.expression(0);if(e.push(t),this._lookahead(0)===d&&(this._match(d),this._lookahead(0)===l))throw new Error("Unexpected token Rbracket")}return this._match(l),{type:"MultiSelectList",children:e}},_parseMultiselectHash:function(){for(var e,t,n,r=[],i=[c,u];;){if(e=this._lookaheadToken(0),i.indexOf(e.type)<0)throw new Error("Expecting an identifier token, got: "+e.type);if(t=e.value,this._advance(),this._match(h),n={type:"KeyValuePair",name:t,value:this.expression(0)},r.push(n),this._lookahead(0)===d)this._match(d);else if(this._lookahead(0)===p){this._match(p);break}}return{type:"MultiSelectHash",children:r}}},W.prototype={search:function(e,t){return this.visit(e,t)},visit:function(e,o){var s,a,c,u,l,f,d,h,p;switch(e.type){case"Field":return null===o?null:n(o)?void 0===(f=o[e.name])?null:f:null;case"Subexpression":for(c=this.visit(e.children[0],o),p=1;p<e.children.length;p++)if(null===(c=this.visit(e.children[1],c)))return null;return c;case"IndexExpression":return d=this.visit(e.children[0],o),this.visit(e.children[1],d);case"Index":if(!t(o))return null;var m=e.value;return m<0&&(m=o.length+m),void 0===(c=o[m])&&(c=null),c;case"Slice":if(!t(o))return null;var T=e.children.slice(0),R=this.computeSliceParams(o.length,T),P=R[0],O=R[1],C=R[2];if(c=[],C>0)for(p=P;p<O;p+=C)c.push(o[p]);else for(p=P;p>O;p+=C)c.push(o[p]);return c;case"Projection":var D=this.visit(e.children[0],o);if(!t(D))return null;for(h=[],p=0;p<D.length;p++)null!==(a=this.visit(e.children[1],D[p]))&&h.push(a);return h;case"ValueProjection":if(!n(D=this.visit(e.children[0],o)))return null;h=[];var M=function(e){for(var t=Object.keys(e),n=[],r=0;r<t.length;r++)n.push(e[t[r]]);return n}(D);for(p=0;p<M.length;p++)null!==(a=this.visit(e.children[1],M[p]))&&h.push(a);return h;case"FilterProjection":if(!t(D=this.visit(e.children[0],o)))return null;var N=[],j=[];for(p=0;p<D.length;p++)i(s=this.visit(e.children[2],D[p]))||N.push(D[p]);for(var q=0;q<N.length;q++)null!==(a=this.visit(e.children[1],N[q]))&&j.push(a);return j;case"Comparator":switch(u=this.visit(e.children[0],o),l=this.visit(e.children[1],o),e.name){case b:c=r(u,l);break;case k:c=!r(u,l);break;case _:c=u>l;break;case S:c=u>=l;break;case w:c=u<l;break;case x:c=u<=l;break;default:throw new Error("Unknown comparator: "+e.name)}return c;case E:var L=this.visit(e.children[0],o);if(!t(L))return null;var F=[];for(p=0;p<L.length;p++)t(a=L[p])?F.push.apply(F,a):F.push(a);return F;case"Identity":return o;case"MultiSelectList":if(null===o)return null;for(h=[],p=0;p<e.children.length;p++)h.push(this.visit(e.children[p],o));return h;case"MultiSelectHash":if(null===o)return null;var I;for(h={},p=0;p<e.children.length;p++)h[(I=e.children[p]).name]=this.visit(I.value,o);return h;case"OrExpression":return i(s=this.visit(e.children[0],o))&&(s=this.visit(e.children[1],o)),s;case"AndExpression":return!0===i(u=this.visit(e.children[0],o))?u:this.visit(e.children[1],o);case"NotExpression":return i(u=this.visit(e.children[0],o));case"Literal":return e.value;case v:return d=this.visit(e.children[0],o),this.visit(e.children[1],d);case g:return o;case"Function":var A=[];for(p=0;p<e.children.length;p++)A.push(this.visit(e.children[p],o));return this.runtime.callFunction(e.name,A);case"ExpressionReference":var W=e.children[0];return W.jmespathType=y,W;default:throw new Error("Unknown node type: "+e.type)}},computeSliceParams:function(e,t){var n=t[0],r=t[1],i=t[2],o=[null,null,null];if(null===i)i=1;else if(0===i){var s=new Error("Invalid slice, step cannot be 0");throw s.name="RuntimeError",s}var a=i<0;return n=null===n?a?e-1:0:this.capSliceRange(e,n,i),r=null===r?a?-1:e:this.capSliceRange(e,r,i),o[0]=n,o[1]=r,o[2]=i,o},capSliceRange:function(e,t,n){return t<0?(t+=e)<0&&(t=n<0?-1:0):t>=e&&(t=n<0?e-1:e),t}},$.prototype={callFunction:function(e,t){var n=this.functionTable[e];if(void 0===n)throw new Error("Unknown function: "+e+"()");return this._validateArgs(e,t,n._signature),n._func.call(this,t)},_validateArgs:function(e,t,n){var r,i,o,s;if(n[n.length-1].variadic){if(t.length<n.length)throw r=1===n.length?" argument":" arguments",new Error("ArgumentError: "+e+"() takes at least"+n.length+r+" but received "+t.length)}else if(t.length!==n.length)throw r=1===n.length?" argument":" arguments",new Error("ArgumentError: "+e+"() takes "+n.length+r+" but received "+t.length);for(var a=0;a<n.length;a++){s=!1,i=n[a].types,o=this._getTypeName(t[a]);for(var c=0;c<i.length;c++)if(this._typeMatches(o,i[c],t[a])){s=!0;break}if(!s)throw new Error("TypeError: "+e+"() expected argument "+(a+1)+" to be type "+i+" but received type "+o+" instead.")}},_typeMatches:function(e,t,n){if(1===t)return!0;if(9!==t&&8!==t&&3!==t)return e===t;if(3===t)return 3===e;if(3===e){var r;8===t?r=s:9===t&&(r=a);for(var i=0;i<n.length;i++)if(!this._typeMatches(this._getTypeName(n[i]),r,n[i]))return!1;return!0}},_getTypeName:function(e){switch(Object.prototype.toString.call(e)){case"[object String]":return a;case"[object Number]":return s;case"[object Array]":return 3;case"[object Boolean]":return 5;case"[object Null]":return 7;case"[object Object]":return e.jmespathType===y?6:4}},_functionStartsWith:function(e){return 0===e[0].lastIndexOf(e[1])},_functionEndsWith:function(e){var t=e[0],n=e[1];return-1!==t.indexOf(n,t.length-n.length)},_functionReverse:function(e){if(this._getTypeName(e[0])===a){for(var t=e[0],n="",r=t.length-1;r>=0;r--)n+=t[r];return n}var i=e[0].slice(0);return i.reverse(),i},_functionAbs:function(e){return Math.abs(e[0])},_functionCeil:function(e){return Math.ceil(e[0])},_functionAvg:function(e){for(var t=0,n=e[0],r=0;r<n.length;r++)t+=n[r];return t/n.length},_functionContains:function(e){return e[0].indexOf(e[1])>=0},_functionFloor:function(e){return Math.floor(e[0])},_functionLength:function(e){return n(e[0])?Object.keys(e[0]).length:e[0].length},_functionMap:function(e){for(var t=[],n=this._interpreter,r=e[0],i=e[1],o=0;o<i.length;o++)t.push(n.visit(r,i[o]));return t},_functionMerge:function(e){for(var t={},n=0;n<e.length;n++){var r=e[n];for(var i in r)t[i]=r[i]}return t},_functionMax:function(e){if(e[0].length>0){if(this._getTypeName(e[0][0])===s)return Math.max.apply(Math,e[0]);for(var t=e[0],n=t[0],r=1;r<t.length;r++)n.localeCompare(t[r])<0&&(n=t[r]);return n}return null},_functionMin:function(e){if(e[0].length>0){if(this._getTypeName(e[0][0])===s)return Math.min.apply(Math,e[0]);for(var t=e[0],n=t[0],r=1;r<t.length;r++)t[r].localeCompare(n)<0&&(n=t[r]);return n}return null},_functionSum:function(e){for(var t=0,n=e[0],r=0;r<n.length;r++)t+=n[r];return t},_functionType:function(e){switch(this._getTypeName(e[0])){case s:return"number";case a:return"string";case 3:return"array";case 4:return"object";case 5:return"boolean";case 6:return"expref";case 7:return"null"}},_functionKeys:function(e){return Object.keys(e[0])},_functionValues:function(e){for(var t=e[0],n=Object.keys(t),r=[],i=0;i<n.length;i++)r.push(t[n[i]]);return r},_functionJoin:function(e){var t=e[0];return e[1].join(t)},_functionToArray:function(e){return 3===this._getTypeName(e[0])?e[0]:[e[0]]},_functionToString:function(e){return this._getTypeName(e[0])===a?e[0]:JSON.stringify(e[0])},_functionToNumber:function(e){var t,n=this._getTypeName(e[0]);return n===s?e[0]:n!==a||(t=+e[0],isNaN(t))?null:t},_functionNotNull:function(e){for(var t=0;t<e.length;t++)if(7!==this._getTypeName(e[t]))return e[t];return null},_functionSort:function(e){var t=e[0].slice(0);return t.sort(),t},_functionSortBy:function(e){var t=e[0].slice(0);if(0===t.length)return t;var n=this._interpreter,r=e[1],i=this._getTypeName(n.visit(r,t[0]));if([s,a].indexOf(i)<0)throw new Error("TypeError");for(var o=this,c=[],u=0;u<t.length;u++)c.push([u,t[u]]);c.sort((function(e,t){var s=n.visit(r,e[1]),a=n.visit(r,t[1]);if(o._getTypeName(s)!==i)throw new Error("TypeError: expected "+i+", received "+o._getTypeName(s));if(o._getTypeName(a)!==i)throw new Error("TypeError: expected "+i+", received "+o._getTypeName(a));return s>a?1:s<a?-1:e[0]-t[0]}));for(var l=0;l<c.length;l++)t[l]=c[l][1];return t},_functionMaxBy:function(e){for(var t,n,r=e[1],i=e[0],o=this.createKeyFunction(r,[s,a]),c=-1/0,u=0;u<i.length;u++)(n=o(i[u]))>c&&(c=n,t=i[u]);return t},_functionMinBy:function(e){for(var t,n,r=e[1],i=e[0],o=this.createKeyFunction(r,[s,a]),c=1/0,u=0;u<i.length;u++)(n=o(i[u]))<c&&(c=n,t=i[u]);return t},createKeyFunction:function(e,t){var n=this,r=this._interpreter;return function(i){var o=r.visit(e,i);if(t.indexOf(n._getTypeName(o))<0){var s="TypeError: expected one of "+t+", received "+n._getTypeName(o);throw new Error(s)}return o}}},e.tokenize=function(e){return(new F).tokenize(e)},e.compile=function(e){return(new A).parse(e)},e.search=function(e,t){var n=new A,r=new $,i=new W(r);r._interpreter=i;var o=n.parse(t);return i.search(o,e)},e.strictDeepEqual=r}(t)},function(e,t,n){"use strict";const r=n(172)(),i=n(173),o=n(26),s=n(71)(),{DATE_FORMAT:a,ERROR_LIKE_KEYS:c,MESSAGE_KEY:u,LEVEL_KEY:l,LEVEL_LABEL:f,TIMESTAMP_KEY:d,LOGGER_KEYS:h,LEVELS:p}=n(45);function m(e,t=!1){if(!1===t)return e;const n=new Date(e);if(!0===t)return i(n,"UTC:"+a);const r=t.toUpperCase();if("SYS:STANDARD"===r)return i(n,a);const o=r.substr(0,4);return i(n,"SYS:"===o||"UTC:"===o?"UTC:"===o?t:t.slice(4):"UTC:"+t)}function g(e){return"[object Object]"===Object.prototype.toString.apply(e)}function y({input:e,ident:t="    ",eol:n="\n"}){const r=e.split(/\r?\n/);for(let e=1;e<r.length;e+=1)r[e]=t+r[e];return r.join(n)}function v({input:e,ident:t="    ",eol:n="\n",skipKeys:r=[],customPrettifiers:i={},errorLikeKeys:a=c,excludeLoggerKeys:u=!0,singleLine:l=!1,colorizer:f=s}){const d=[].concat(r);!0===u&&Array.prototype.push.apply(d,h);let p="";const{plain:m,errors:g}=Object.entries(e).reduce(({plain:t,errors:n},[r,o])=>{if(!1===d.includes(r)){const s="function"==typeof i[r]?i[r](o,r,e):o;a.includes(r)?n[r]=s:t[r]=s}return{plain:t,errors:n}},{plain:{},errors:{}});return l?(Object.keys(m).length>0&&(p+=f.greyMessage(o(m))),p+=n):Object.entries(m).forEach(([e,r])=>{const s="function"==typeof i[e]?r:o(r,null,2);if(void 0===s)return;const a=y({input:s,ident:t,eol:n});p+=`${t}${e}: ${a}${n}`}),Object.entries(g).forEach(([e,r])=>{const s="function"==typeof i[e]?r:o(r,null,2);void 0!==s&&(p+=b({keyName:e,lines:s,eol:n,ident:t}))}),p}function b({keyName:e,lines:t,eol:n,ident:r}){let i="";const o=`${r}${e}: ${y({input:t,ident:r,eol:n})}${n}`.split(n);for(let e=0;e<o.length;e+=1){0!==e&&(i+=n);const t=o[e];if(/^\s*"stack"/.test(t)){const e=/^(\s*"stack":)\s*(".*"),?$/.exec(t);if(e&&3===e.length){const r=/^\s*/.exec(t)[0].length+4,o=" ".repeat(r),s=e[2];i+=e[1]+n+o+JSON.parse(s).replace(/\n/g,n+o)}}else i+=t}return i}function _(e,t){const n=t.split("."),r=n.pop();n.forEach(t=>{Object.prototype.hasOwnProperty.call(e,t)&&(e=e[t])}),delete e[r]}e.exports={isObject:g,prettifyErrorLog:function({log:e,messageKey:t=u,ident:n="    ",eol:r="\n",errorLikeKeys:i=c,errorProperties:o=[]}){let s=`${n}${y({input:e.stack,ident:n,eol:r})}${r}`;if(o.length>0){const a=h.concat(t,"type","stack");let c;c="*"===o[0]?Object.keys(e).filter(e=>!1===a.includes(e)):o.filter(e=>!1===a.includes(e));for(let t=0;t<c.length;t+=1){const o=c[t];if(o in e!=0)if(g(e[o])){s=`${s}${o}: {${r}${v({input:e[o],errorLikeKeys:i,excludeLoggerKeys:!1,eol:r,ident:n})}}${r}`}else s=`${s}${o}: ${e[o]}${r}`}}return s},prettifyLevel:function({log:e,colorizer:t=s,levelKey:n=l}){return n in e==0?void 0:t(e[n])},prettifyMessage:function({log:e,messageFormat:t,messageKey:n=u,colorizer:r=s,levelLabel:i=f}){if(t&&"string"==typeof t){const n=String(t).replace(/{([^{}]+)}/g,(function(t,n){return n===i&&e[l]?p[e[l]]:n.split(".").reduce((function(e,t){return e&&e[t]?e[t]:""}),e)}));return r.message(n)}if(t&&"function"==typeof t){const o=t(e,n,i);return r.message(o)}return n in e==0||"string"!=typeof e[n]?void 0:r.message(e[n])},prettifyMetadata:function({log:e}){let t="";return(e.name||e.pid||e.hostname)&&(t+="(",e.name&&(t+=e.name),e.name&&e.pid?t+="/"+e.pid:e.pid&&(t+=e.pid),e.hostname&&(t+=`${"("===t?"on":" on"} ${e.hostname}`),t+=")"),e.caller&&(t+=`${""===t?"":" "}<${e.caller}>`),""===t?void 0:t},prettifyObject:v,prettifyTime:function({log:e,timestampKey:t=d,translateFormat:n}){let r=null;if(t in e?r=e[t]:"timestamp"in e&&(r=e.timestamp),null!==r)return n?"["+m(r,n)+"]":`[${r}]`},filterLog:function(e,t){const n=r(e);return t.forEach(e=>{_(n,e)}),n}},e.exports.internals={formatTime:m,joinLinesWithIndentation:y,prettifyError:b,deleteLogProperty:_}},function(e,t,n){"use strict";function r(e){return e instanceof Buffer?Buffer.from(e):new e.constructor(e.buffer.slice(),e.byteOffset,e.length)}e.exports=function(e){return(e=e||{}).circles?function(e){var t=[],n=[];return e.proto?function e(o){if("object"!=typeof o||null===o)return o;if(o instanceof Date)return new Date(o);if(Array.isArray(o))return i(o,e);if(o instanceof Map)return new Map(i(Array.from(o),e));if(o instanceof Set)return new Set(i(Array.from(o),e));var s={};for(var a in t.push(o),n.push(s),o){var c=o[a];if("object"!=typeof c||null===c)s[a]=c;else if(c instanceof Date)s[a]=new Date(c);else if(c instanceof Map)s[a]=new Map(i(Array.from(c),e));else if(c instanceof Set)s[a]=new Set(i(Array.from(c),e));else if(ArrayBuffer.isView(c))s[a]=r(c);else{var u=t.indexOf(c);s[a]=-1!==u?n[u]:e(c)}}return t.pop(),n.pop(),s}:function e(o){if("object"!=typeof o||null===o)return o;if(o instanceof Date)return new Date(o);if(Array.isArray(o))return i(o,e);if(o instanceof Map)return new Map(i(Array.from(o),e));if(o instanceof Set)return new Set(i(Array.from(o),e));var s={};for(var a in t.push(o),n.push(s),o)if(!1!==Object.hasOwnProperty.call(o,a)){var c=o[a];if("object"!=typeof c||null===c)s[a]=c;else if(c instanceof Date)s[a]=new Date(c);else if(c instanceof Map)s[a]=new Map(i(Array.from(c),e));else if(c instanceof Set)s[a]=new Set(i(Array.from(c),e));else if(ArrayBuffer.isView(c))s[a]=r(c);else{var u=t.indexOf(c);s[a]=-1!==u?n[u]:e(c)}}return t.pop(),n.pop(),s};function i(e,i){for(var o=Object.keys(e),s=new Array(o.length),a=0;a<o.length;a++){var c=o[a],u=e[c];if("object"!=typeof u||null===u)s[c]=u;else if(u instanceof Date)s[c]=new Date(u);else if(ArrayBuffer.isView(u))s[c]=r(u);else{var l=t.indexOf(u);s[c]=-1!==l?n[l]:i(u)}}return s}}(e):e.proto?function e(n){if("object"!=typeof n||null===n)return n;if(n instanceof Date)return new Date(n);if(Array.isArray(n))return t(n,e);if(n instanceof Map)return new Map(t(Array.from(n),e));if(n instanceof Set)return new Set(t(Array.from(n),e));var i={};for(var o in n){var s=n[o];"object"!=typeof s||null===s?i[o]=s:s instanceof Date?i[o]=new Date(s):s instanceof Map?i[o]=new Map(t(Array.from(s),e)):s instanceof Set?i[o]=new Set(t(Array.from(s),e)):ArrayBuffer.isView(s)?i[o]=r(s):i[o]=e(s)}return i}:function e(n){if("object"!=typeof n||null===n)return n;if(n instanceof Date)return new Date(n);if(Array.isArray(n))return t(n,e);if(n instanceof Map)return new Map(t(Array.from(n),e));if(n instanceof Set)return new Set(t(Array.from(n),e));var i={};for(var o in n)if(!1!==Object.hasOwnProperty.call(n,o)){var s=n[o];"object"!=typeof s||null===s?i[o]=s:s instanceof Date?i[o]=new Date(s):s instanceof Map?i[o]=new Map(t(Array.from(s),e)):s instanceof Set?i[o]=new Set(t(Array.from(s),e)):ArrayBuffer.isView(s)?i[o]=r(s):i[o]=e(s)}return i};function t(e,t){for(var n=Object.keys(e),i=new Array(n.length),o=0;o<n.length;o++){var s=n[o],a=e[s];"object"!=typeof a||null===a?i[s]=a:a instanceof Date?i[s]=new Date(a):ArrayBuffer.isView(a)?i[s]=r(a):i[s]=t(a)}return i}}},function(e,t,n){"use strict";var r;function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(o){var s,a,c,u=arguments,l=(s=/d{1,4}|D{3,4}|m{1,4}|yy(?:yy)?|([HhMsTt])\1?|W{1,2}|[LlopSZN]|"[^"]*"|'[^']*'/g,a=/\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\d{4})?)\b/g,c=/[^-+\dA-Z]/g,function(e,t,n,r){if(1!==u.length||"string"!==m(e)||/\d/.test(e)||(t=e,e=void 0),(e=e||0===e?e:new Date)instanceof Date||(e=new Date(e)),isNaN(e))throw TypeError("Invalid date");var i=(t=String(l.masks[t]||t||l.masks.default)).slice(0,4);"UTC:"!==i&&"GMT:"!==i||(t=t.slice(4),n=!0,"GMT:"===i&&(r=!0));var o=function(){return n?"getUTC":"get"},g=function(){return e[o()+"Date"]()},y=function(){return e[o()+"Day"]()},v=function(){return e[o()+"Month"]()},b=function(){return e[o()+"FullYear"]()},_=function(){return e[o()+"Hours"]()},w=function(){return e[o()+"Minutes"]()},S=function(){return e[o()+"Seconds"]()},x=function(){return e[o()+"Milliseconds"]()},k=function(){return n?0:e.getTimezoneOffset()},E=function(){return h(e)},T={d:function(){return g()},dd:function(){return f(g())},ddd:function(){return l.i18n.dayNames[y()]},DDD:function(){return d({y:b(),m:v(),d:g(),_:o(),dayName:l.i18n.dayNames[y()],short:!0})},dddd:function(){return l.i18n.dayNames[y()+7]},DDDD:function(){return d({y:b(),m:v(),d:g(),_:o(),dayName:l.i18n.dayNames[y()+7]})},m:function(){return v()+1},mm:function(){return f(v()+1)},mmm:function(){return l.i18n.monthNames[v()]},mmmm:function(){return l.i18n.monthNames[v()+12]},yy:function(){return String(b()).slice(2)},yyyy:function(){return f(b(),4)},h:function(){return _()%12||12},hh:function(){return f(_()%12||12)},H:function(){return _()},HH:function(){return f(_())},M:function(){return w()},MM:function(){return f(w())},s:function(){return S()},ss:function(){return f(S())},l:function(){return f(x(),3)},L:function(){return f(Math.floor(x()/10))},t:function(){return _()<12?l.i18n.timeNames[0]:l.i18n.timeNames[1]},tt:function(){return _()<12?l.i18n.timeNames[2]:l.i18n.timeNames[3]},T:function(){return _()<12?l.i18n.timeNames[4]:l.i18n.timeNames[5]},TT:function(){return _()<12?l.i18n.timeNames[6]:l.i18n.timeNames[7]},Z:function(){return r?"GMT":n?"UTC":(String(e).match(a)||[""]).pop().replace(c,"").replace(/GMT\+0000/g,"UTC")},o:function(){return(k()>0?"-":"+")+f(100*Math.floor(Math.abs(k())/60)+Math.abs(k())%60,4)},p:function(){return(k()>0?"-":"+")+f(Math.floor(Math.abs(k())/60),2)+":"+f(Math.floor(Math.abs(k())%60),2)},S:function(){return["th","st","nd","rd"][g()%10>3?0:(g()%100-g()%10!=10)*g()%10]},W:function(){return E()},WW:function(){return f(E())},N:function(){return p(e)}};return t.replace(s,(function(e){return e in T?T[e]():e.slice(1,e.length-1)}))});l.masks={default:"ddd mmm dd yyyy HH:MM:ss",shortDate:"m/d/yy",paddedShortDate:"mm/dd/yyyy",mediumDate:"mmm d, yyyy",longDate:"mmmm d, yyyy",fullDate:"dddd, mmmm d, yyyy",shortTime:"h:MM TT",mediumTime:"h:MM:ss TT",longTime:"h:MM:ss TT Z",isoDate:"yyyy-mm-dd",isoTime:"HH:MM:ss",isoDateTime:"yyyy-mm-dd'T'HH:MM:sso",isoUtcDateTime:"UTC:yyyy-mm-dd'T'HH:MM:ss'Z'",expiresHeaderFormat:"ddd, dd mmm yyyy HH:MM:ss Z"},l.i18n={dayNames:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],monthNames:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","January","February","March","April","May","June","July","August","September","October","November","December"],timeNames:["a","p","am","pm","A","P","AM","PM"]};var f=function(e,t){for(e=String(e),t=t||2;e.length<t;)e="0"+e;return e},d=function(e){var t=e.y,n=e.m,r=e.d,i=e._,o=e.dayName,s=e.short,a=void 0!==s&&s,c=new Date,u=new Date;u.setDate(u[i+"Date"]()-1);var l=new Date;return l.setDate(l[i+"Date"]()+1),c[i+"FullYear"]()===t&&c[i+"Month"]()===n&&c[i+"Date"]()===r?a?"Tdy":"Today":u[i+"FullYear"]()===t&&u[i+"Month"]()===n&&u[i+"Date"]()===r?a?"Ysd":"Yesterday":l[i+"FullYear"]()===t&&l[i+"Month"]()===n&&l[i+"Date"]()===r?a?"Tmw":"Tomorrow":o},h=function(e){var t=new Date(e.getFullYear(),e.getMonth(),e.getDate());t.setDate(t.getDate()-(t.getDay()+6)%7+3);var n=new Date(t.getFullYear(),0,4);n.setDate(n.getDate()-(n.getDay()+6)%7+3);var r=t.getTimezoneOffset()-n.getTimezoneOffset();t.setHours(t.getHours()-r);var i=(t-n)/6048e5;return 1+Math.floor(i)},p=function(e){var t=e.getDay();return 0===t&&(t=7),t},m=function(e){return null===e?"null":void 0===e?"undefined":"object"!==i(e)?i(e):Array.isArray(e)?"array":{}.toString.call(e).slice(8,-1).toLowerCase()};void 0===(r=function(){return l}.call(t,n,t,e))||(e.exports=r)}(void 0)},function(e,t,n){"use strict";const r=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*\:/;t.parse=function(e,...n){const i="object"==typeof n[0]&&n[0],o=n.length>1||!i?n[0]:void 0,s=n.length>1&&n[1]||i||{},a=JSON.parse(e,o);return"ignore"===s.protoAction?a:a&&"object"==typeof a&&e.match(r)?(t.scan(a,s),a):a},t.scan=function(e,t={}){let n=[e];for(;n.length;){const e=n;n=[];for(const r of e){if(Object.prototype.hasOwnProperty.call(r,"__proto__")){if("remove"!==t.protoAction)throw new SyntaxError("Object contains forbidden prototype property");delete r.__proto__}for(const e in r){const t=r[e];t&&"object"==typeof t&&n.push(r[e])}}}},t.safeParse=function(e,n){try{return t.parse(e,n)}catch(e){return null}}},function(e){e.exports=JSON.parse('{"name":"pino","version":"6.13.1","description":"super fast, all natural json logger","main":"pino.js","browser":"./browser.js","files":["pino.js","bin.js","browser.js","pretty.js","usage.txt","test","docs","example.js","lib"],"scripts":{"docs":"docsify serve","browser-test":"airtap --local 8080 test/browser*test.js","lint":"eslint .","test":"npm run lint && tap --100 test/*test.js test/*/*test.js","test-ci":"npm run lint && tap test/*test.js test/*/*test.js --coverage-report=lcovonly","cov-ui":"tap --coverage-report=html test/*test.js test/*/*test.js","bench":"node benchmarks/utils/runbench all","bench-basic":"node benchmarks/utils/runbench basic","bench-object":"node benchmarks/utils/runbench object","bench-deep-object":"node benchmarks/utils/runbench deep-object","bench-multi-arg":"node benchmarks/utils/runbench multi-arg","bench-longs-tring":"node benchmarks/utils/runbench long-string","bench-child":"node benchmarks/utils/runbench child","bench-child-child":"node benchmarks/utils/runbench child-child","bench-child-creation":"node benchmarks/utils/runbench child-creation","bench-formatters":"node benchmarks/utils/runbench formatters","update-bench-doc":"node benchmarks/utils/generate-benchmark-doc > docs/benchmarks.md"},"bin":{"pino":"./bin.js"},"precommit":"test","repository":{"type":"git","url":"git+https://github.com/pinojs/pino.git"},"keywords":["fast","logger","stream","json"],"author":"Matteo Collina <<EMAIL>>","contributors":["David Mark Clements <<EMAIL>>","James Sumners <<EMAIL>>","Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)"],"license":"MIT","bugs":{"url":"https://github.com/pinojs/pino/issues"},"homepage":"http://getpino.io","devDependencies":{"airtap":"4.0.3","benchmark":"^2.1.4","bole":"^4.0.0","bunyan":"^1.8.14","docsify-cli":"^4.4.1","eslint":"^7.17.0","eslint-config-standard":"^16.0.2","eslint-plugin-import":"^2.22.1","eslint-plugin-node":"^11.1.0","eslint-plugin-promise":"^5.1.0","execa":"^5.0.0","fastbench":"^1.0.1","flush-write-stream":"^2.0.0","import-fresh":"^3.2.1","log":"^6.0.0","loglevel":"^1.6.7","pino-pretty":"^4.1.0","pre-commit":"^1.2.2","proxyquire":"^2.1.3","pump":"^3.0.0","semver":"^7.0.0","split2":"^3.1.1","steed":"^1.1.3","strip-ansi":"^6.0.0","tap":"^15.0.1","tape":"^5.0.0","through2":"^4.0.0","winston":"^3.3.3"},"dependencies":{"fast-redact":"^3.0.0","fast-safe-stringify":"^2.0.8","fastify-warning":"^0.2.0","flatstr":"^1.0.12","pino-std-serializers":"^3.1.0","quick-format-unescaped":"^4.0.3","sonic-boom":"^1.0.2"}}')},,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(22);var i;!function(e){e.ParseError=-32700,e.InvalidRequest=-32600,e.MethodNotFound=-32601,e.InvalidParams=-32602,e.InternalError=-32603,e.serverErrorStart=-32099,e.serverErrorEnd=-32e3,e.ServerNotInitialized=-32002,e.UnknownErrorCode=-32001,e.RequestCancelled=-32800,e.ContentModified=-32801,e.MessageWriteError=1,e.MessageReadError=2}(i=t.ErrorCodes||(t.ErrorCodes={}));class o extends Error{constructor(e,t,n){super(t),this.code=r.number(e)?e:i.UnknownErrorCode,this.data=n,Object.setPrototypeOf(this,o.prototype)}toJson(){return{code:this.code,message:this.message,data:this.data}}}t.ResponseError=o;class s{constructor(e,t){this._method=e,this._numberOfParams=t}get method(){return this._method}get numberOfParams(){return this._numberOfParams}}t.AbstractMessageType=s,t.RequestType0=class extends s{constructor(e){super(e,0)}},t.RequestType=class extends s{constructor(e){super(e,1)}},t.RequestType1=class extends s{constructor(e){super(e,1)}},t.RequestType2=class extends s{constructor(e){super(e,2)}},t.RequestType3=class extends s{constructor(e){super(e,3)}},t.RequestType4=class extends s{constructor(e){super(e,4)}},t.RequestType5=class extends s{constructor(e){super(e,5)}},t.RequestType6=class extends s{constructor(e){super(e,6)}},t.RequestType7=class extends s{constructor(e){super(e,7)}},t.RequestType8=class extends s{constructor(e){super(e,8)}},t.RequestType9=class extends s{constructor(e){super(e,9)}},t.NotificationType=class extends s{constructor(e){super(e,1),this._=void 0}},t.NotificationType0=class extends s{constructor(e){super(e,0)}},t.NotificationType1=class extends s{constructor(e){super(e,1)}},t.NotificationType2=class extends s{constructor(e){super(e,2)}},t.NotificationType3=class extends s{constructor(e){super(e,3)}},t.NotificationType4=class extends s{constructor(e){super(e,4)}},t.NotificationType5=class extends s{constructor(e){super(e,5)}},t.NotificationType6=class extends s{constructor(e){super(e,6)}},t.NotificationType7=class extends s{constructor(e){super(e,7)}},t.NotificationType8=class extends s{constructor(e){super(e,8)}},t.NotificationType9=class extends s{constructor(e){super(e,9)}},t.isRequestMessage=function(e){let t=e;return t&&r.string(t.method)&&(r.string(t.id)||r.number(t.id))},t.isNotificationMessage=function(e){let t=e;return t&&r.string(t.method)&&void 0===e.id},t.isResponseMessage=function(e){let t=e;return t&&(void 0!==t.result||!!t.error)&&(r.string(t.id)||r.number(t.id)||null===t.id)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(35),i=n(22);var o;!function(e){e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:r.Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:r.Event.None}),e.is=function(t){let n=t;return n&&(n===e.None||n===e.Cancelled||i.boolean(n.isCancellationRequested)&&!!n.onCancellationRequested)}}(o=t.CancellationToken||(t.CancellationToken={}));const s=Object.freeze((function(e,t){let n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}));class a{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?s:(this._emitter||(this._emitter=new r.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}t.CancellationTokenSource=class{get token(){return this._token||(this._token=new a),this._token}cancel(){this._token?this._token.cancel():this._token=o.Cancelled}dispose(){this._token?this._token instanceof a&&this._token.dispose():this._token=o.None}}},function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.None=0,e.First=1,e.Last=2}(r=t.Touch||(t.Touch={})),t.LinkedMap=class{constructor(){this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}has(e){return this._map.has(e)}get(e){const t=this._map.get(e);if(t)return t.value}set(e,t,n=r.None){let i=this._map.get(e);if(i)i.value=t,n!==r.None&&this.touch(i,n);else{switch(i={key:e,value:t,next:void 0,previous:void 0},n){case r.None:this.addItemLast(i);break;case r.First:this.addItemFirst(i);break;case r.Last:default:this.addItemLast(i)}this._map.set(e,i),this._size++}}delete(e){const t=this._map.get(e);return!!t&&(this._map.delete(e),this.removeItem(t),this._size--,!0)}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,t){let n=this._head;for(;n;)t?e.bind(t)(n.value,n.key,this):e(n.value,n.key,this),n=n.next}forEachReverse(e,t){let n=this._tail;for(;n;)t?e.bind(t)(n.value,n.key,this):e(n.value,n.key,this),n=n.previous}values(){let e=[],t=this._head;for(;t;)e.push(t.value),t=t.next;return e}keys(){let e=[],t=this._head;for(;t;)e.push(t.key),t=t.next;return e}addItemFirst(e){if(this._head||this._tail){if(!this._head)throw new Error("Invalid list");e.next=this._head,this._head.previous=e}else this._tail=e;this._head=e}addItemLast(e){if(this._head||this._tail){if(!this._tail)throw new Error("Invalid list");e.previous=this._tail,this._tail.next=e}else this._head=e;this._tail=e}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head)this._head=e.next;else if(e===this._tail)this._tail=e.previous;else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}}touch(e,t){if(!this._head||!this._tail)throw new Error("Invalid list");if(t===r.First||t===r.Last)if(t===r.First){if(e===this._head)return;const t=e.next,n=e.previous;e===this._tail?(n.next=void 0,this._tail=n):(t.previous=n,n.next=t),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e}else if(t===r.Last){if(e===this._tail)return;const t=e.next,n=e.previous;e===this._head?(t.previous=void 0,this._head=t):(t.previous=n,n.next=t),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(0),i=n(3),o=n(91),s=n(74),a=n(47),c=n(48);t.generateRandomPipeName=function(){const e=o.randomBytes(21).toString("hex");return"win32"===process.platform?`\\\\.\\pipe\\vscode-jsonrpc-${e}-sock`:r.join(i.tmpdir(),`vscode-${e}.sock`)},t.createClientPipeTransport=function(e,t="utf-8"){let n,r=new Promise((e,t)=>{n=e});return new Promise((i,o)=>{let u=s.createServer(e=>{u.close(),n([new a.SocketMessageReader(e,t),new c.SocketMessageWriter(e,t)])});u.on("error",o),u.listen(e,()=>{u.removeListener("error",o),i({onConnected:()=>r})})})},t.createServerPipeTransport=function(e,t="utf-8"){const n=s.createConnection(e);return[new a.SocketMessageReader(n,t),new c.SocketMessageWriter(n,t)]}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(74),i=n(47),o=n(48);t.createClientSocketTransport=function(e,t="utf-8"){let n,s=new Promise((e,t)=>{n=e});return new Promise((a,c)=>{let u=r.createServer(e=>{u.close(),n([new i.SocketMessageReader(e,t),new o.SocketMessageWriter(e,t)])});u.on("error",c),u.listen(e,"127.0.0.1",()=>{u.removeListener("error",c),a({onConnected:()=>s})})})},t.createServerSocketTransport=function(e,t="utf-8"){const n=r.createConnection(e,"127.0.0.1");return[new i.SocketMessageReader(n,t),new o.SocketMessageWriter(n,t)]}},function(e,t,n){"use strict";var r,i,o,s,a,c,u,l,f,d,h,p,m,g,y,v,b,_,w,S;n.r(t),n.d(t,"Position",(function(){return r})),n.d(t,"Range",(function(){return i})),n.d(t,"Location",(function(){return o})),n.d(t,"LocationLink",(function(){return s})),n.d(t,"Color",(function(){return a})),n.d(t,"ColorInformation",(function(){return c})),n.d(t,"ColorPresentation",(function(){return u})),n.d(t,"FoldingRangeKind",(function(){return l})),n.d(t,"FoldingRange",(function(){return f})),n.d(t,"DiagnosticRelatedInformation",(function(){return d})),n.d(t,"DiagnosticSeverity",(function(){return h})),n.d(t,"DiagnosticTag",(function(){return p})),n.d(t,"Diagnostic",(function(){return m})),n.d(t,"Command",(function(){return g})),n.d(t,"TextEdit",(function(){return y})),n.d(t,"TextDocumentEdit",(function(){return v})),n.d(t,"CreateFile",(function(){return b})),n.d(t,"RenameFile",(function(){return _})),n.d(t,"DeleteFile",(function(){return w})),n.d(t,"WorkspaceEdit",(function(){return S})),n.d(t,"WorkspaceChange",(function(){return Z})),n.d(t,"TextDocumentIdentifier",(function(){return x})),n.d(t,"VersionedTextDocumentIdentifier",(function(){return k})),n.d(t,"TextDocumentItem",(function(){return E})),n.d(t,"MarkupKind",(function(){return T})),n.d(t,"MarkupContent",(function(){return R})),n.d(t,"CompletionItemKind",(function(){return P})),n.d(t,"InsertTextFormat",(function(){return O})),n.d(t,"CompletionItemTag",(function(){return C})),n.d(t,"CompletionItem",(function(){return D})),n.d(t,"CompletionList",(function(){return M})),n.d(t,"MarkedString",(function(){return N})),n.d(t,"Hover",(function(){return j})),n.d(t,"ParameterInformation",(function(){return q})),n.d(t,"SignatureInformation",(function(){return L})),n.d(t,"DocumentHighlightKind",(function(){return F})),n.d(t,"DocumentHighlight",(function(){return I})),n.d(t,"SymbolKind",(function(){return A})),n.d(t,"SymbolTag",(function(){return W})),n.d(t,"SymbolInformation",(function(){return $})),n.d(t,"DocumentSymbol",(function(){return H})),n.d(t,"CodeActionKind",(function(){return z})),n.d(t,"CodeActionContext",(function(){return B})),n.d(t,"CodeAction",(function(){return U})),n.d(t,"CodeLens",(function(){return K})),n.d(t,"FormattingOptions",(function(){return J})),n.d(t,"DocumentLink",(function(){return V})),n.d(t,"SelectionRange",(function(){return G})),n.d(t,"EOL",(function(){return X})),n.d(t,"TextDocument",(function(){return Q})),function(e){e.create=function(e,t){return{line:e,character:t}},e.is=function(e){var t=e;return ee.objectLiteral(t)&&ee.number(t.line)&&ee.number(t.character)}}(r||(r={})),function(e){e.create=function(e,t,n,i){if(ee.number(e)&&ee.number(t)&&ee.number(n)&&ee.number(i))return{start:r.create(e,t),end:r.create(n,i)};if(r.is(e)&&r.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+n+", "+i+"]")},e.is=function(e){var t=e;return ee.objectLiteral(t)&&r.is(t.start)&&r.is(t.end)}}(i||(i={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return ee.defined(t)&&i.is(t.range)&&(ee.string(t.uri)||ee.undefined(t.uri))}}(o||(o={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return ee.defined(t)&&i.is(t.targetRange)&&ee.string(t.targetUri)&&(i.is(t.targetSelectionRange)||ee.undefined(t.targetSelectionRange))&&(i.is(t.originSelectionRange)||ee.undefined(t.originSelectionRange))}}(s||(s={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return ee.number(t.red)&&ee.number(t.green)&&ee.number(t.blue)&&ee.number(t.alpha)}}(a||(a={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return i.is(t.range)&&a.is(t.color)}}(c||(c={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return ee.string(t.label)&&(ee.undefined(t.textEdit)||y.is(t))&&(ee.undefined(t.additionalTextEdits)||ee.typedArray(t.additionalTextEdits,y.is))}}(u||(u={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(l||(l={})),function(e){e.create=function(e,t,n,r,i){var o={startLine:e,endLine:t};return ee.defined(n)&&(o.startCharacter=n),ee.defined(r)&&(o.endCharacter=r),ee.defined(i)&&(o.kind=i),o},e.is=function(e){var t=e;return ee.number(t.startLine)&&ee.number(t.startLine)&&(ee.undefined(t.startCharacter)||ee.number(t.startCharacter))&&(ee.undefined(t.endCharacter)||ee.number(t.endCharacter))&&(ee.undefined(t.kind)||ee.string(t.kind))}}(f||(f={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return ee.defined(t)&&o.is(t.location)&&ee.string(t.message)}}(d||(d={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(h||(h={})),function(e){e.Unnecessary=1,e.Deprecated=2}(p||(p={})),function(e){e.create=function(e,t,n,r,i,o){var s={range:e,message:t};return ee.defined(n)&&(s.severity=n),ee.defined(r)&&(s.code=r),ee.defined(i)&&(s.source=i),ee.defined(o)&&(s.relatedInformation=o),s},e.is=function(e){var t=e;return ee.defined(t)&&i.is(t.range)&&ee.string(t.message)&&(ee.number(t.severity)||ee.undefined(t.severity))&&(ee.number(t.code)||ee.string(t.code)||ee.undefined(t.code))&&(ee.string(t.source)||ee.undefined(t.source))&&(ee.undefined(t.relatedInformation)||ee.typedArray(t.relatedInformation,d.is))}}(m||(m={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={title:e,command:t};return ee.defined(n)&&n.length>0&&(i.arguments=n),i},e.is=function(e){var t=e;return ee.defined(t)&&ee.string(t.title)&&ee.string(t.command)}}(g||(g={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return ee.objectLiteral(t)&&ee.string(t.newText)&&i.is(t.range)}}(y||(y={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return ee.defined(t)&&k.is(t.textDocument)&&Array.isArray(t.edits)}}(v||(v={})),function(e){e.create=function(e,t){var n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),n},e.is=function(e){var t=e;return t&&"create"===t.kind&&ee.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||ee.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||ee.boolean(t.options.ignoreIfExists)))}}(b||(b={})),function(e){e.create=function(e,t,n){var r={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(r.options=n),r},e.is=function(e){var t=e;return t&&"rename"===t.kind&&ee.string(t.oldUri)&&ee.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||ee.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||ee.boolean(t.options.ignoreIfExists)))}}(_||(_={})),function(e){e.create=function(e,t){var n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),n},e.is=function(e){var t=e;return t&&"delete"===t.kind&&ee.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||ee.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||ee.boolean(t.options.ignoreIfNotExists)))}}(w||(w={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return ee.string(e.kind)?b.is(e)||_.is(e)||w.is(e):v.is(e)})))}}(S||(S={}));var x,k,E,T,R,P,O,C,D,M,N,j,q,L,F,I,A,W,$,H,z,B,U,K,J,V,G,Y=function(){function e(e){this.edits=e}return e.prototype.insert=function(e,t){this.edits.push(y.insert(e,t))},e.prototype.replace=function(e,t){this.edits.push(y.replace(e,t))},e.prototype.delete=function(e){this.edits.push(y.del(e))},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e}(),Z=function(){function e(e){var t=this;this._textEditChanges=Object.create(null),e&&(this._workspaceEdit=e,e.documentChanges?e.documentChanges.forEach((function(e){if(v.is(e)){var n=new Y(e.edits);t._textEditChanges[e.textDocument.uri]=n}})):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new Y(e.changes[n]);t._textEditChanges[n]=r})))}return Object.defineProperty(e.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),e.prototype.getTextEditChange=function(e){if(k.is(e)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t=e;if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:i=[]};this._workspaceEdit.documentChanges.push(n),r=new Y(i),this._textEditChanges[t.uri]=r}return r}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var i=[];this._workspaceEdit.changes[e]=i,r=new Y(i),this._textEditChanges[e]=r}return r},e.prototype.createFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(b.create(e,t))},e.prototype.renameFile=function(e,t,n){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(_.create(e,t,n))},e.prototype.deleteFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(w.create(e,t))},e.prototype.checkDocumentChanges=function(){if(!this._workspaceEdit||!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.")},e}();!function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return ee.defined(t)&&ee.string(t.uri)}}(x||(x={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return ee.defined(t)&&ee.string(t.uri)&&(null===t.version||ee.number(t.version))}}(k||(k={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return ee.defined(t)&&ee.string(t.uri)&&ee.string(t.languageId)&&ee.number(t.version)&&ee.string(t.text)}}(E||(E={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(T||(T={})),function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(T||(T={})),function(e){e.is=function(e){var t=e;return ee.objectLiteral(e)&&T.is(t.kind)&&ee.string(t.value)}}(R||(R={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(P||(P={})),function(e){e.PlainText=1,e.Snippet=2}(O||(O={})),function(e){e.Deprecated=1}(C||(C={})),function(e){e.create=function(e){return{label:e}}}(D||(D={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(M||(M={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return ee.string(t)||ee.objectLiteral(t)&&ee.string(t.language)&&ee.string(t.value)}}(N||(N={})),function(e){e.is=function(e){var t=e;return!!t&&ee.objectLiteral(t)&&(R.is(t.contents)||N.is(t.contents)||ee.typedArray(t.contents,N.is))&&(void 0===e.range||i.is(e.range))}}(j||(j={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(q||(q={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i={label:e};return ee.defined(t)&&(i.documentation=t),ee.defined(n)?i.parameters=n:i.parameters=[],i}}(L||(L={})),function(e){e.Text=1,e.Read=2,e.Write=3}(F||(F={})),function(e){e.create=function(e,t){var n={range:e};return ee.number(t)&&(n.kind=t),n}}(I||(I={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(A||(A={})),function(e){e.Deprecated=1}(W||(W={})),function(e){e.create=function(e,t,n,r,i){var o={name:e,kind:t,location:{uri:r,range:n}};return i&&(o.containerName=i),o}}($||($={})),function(e){e.create=function(e,t,n,r,i,o){var s={name:e,detail:t,kind:n,range:r,selectionRange:i};return void 0!==o&&(s.children=o),s},e.is=function(e){var t=e;return t&&ee.string(t.name)&&ee.number(t.kind)&&i.is(t.range)&&i.is(t.selectionRange)&&(void 0===t.detail||ee.string(t.detail))&&(void 0===t.deprecated||ee.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))}}(H||(H={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(z||(z={})),function(e){e.create=function(e,t){var n={diagnostics:e};return null!=t&&(n.only=t),n},e.is=function(e){var t=e;return ee.defined(t)&&ee.typedArray(t.diagnostics,m.is)&&(void 0===t.only||ee.typedArray(t.only,ee.string))}}(B||(B={})),function(e){e.create=function(e,t,n){var r={title:e};return g.is(t)?r.command=t:r.edit=t,void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&ee.string(t.title)&&(void 0===t.diagnostics||ee.typedArray(t.diagnostics,m.is))&&(void 0===t.kind||ee.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||g.is(t.command))&&(void 0===t.isPreferred||ee.boolean(t.isPreferred))&&(void 0===t.edit||S.is(t.edit))}}(U||(U={})),function(e){e.create=function(e,t){var n={range:e};return ee.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return ee.defined(t)&&i.is(t.range)&&(ee.undefined(t.command)||g.is(t.command))}}(K||(K={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return ee.defined(t)&&ee.number(t.tabSize)&&ee.boolean(t.insertSpaces)}}(J||(J={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return ee.defined(t)&&i.is(t.range)&&(ee.undefined(t.target)||ee.string(t.target))}}(V||(V={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){var n=t;return void 0!==n&&i.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(G||(G={}));var Q,X=["\n","\r\n","\r"];!function(e){e.create=function(e,t,n,r){return new te(e,t,n,r)},e.is=function(e){var t=e;return!!(ee.defined(t)&&ee.string(t.uri)&&(ee.undefined(t.languageId)||ee.string(t.languageId))&&ee.number(t.lineCount)&&ee.func(t.getText)&&ee.func(t.positionAt)&&ee.func(t.offsetAt))},e.applyEdits=function(e,t){for(var n=e.getText(),r=function e(t,n){if(t.length<=1)return t;var r=t.length/2|0,i=t.slice(0,r),o=t.slice(r);e(i,n),e(o,n);for(var s=0,a=0,c=0;s<i.length&&a<o.length;){var u=n(i[s],o[a]);t[c++]=u<=0?i[s++]:o[a++]}for(;s<i.length;)t[c++]=i[s++];for(;a<o.length;)t[c++]=o[a++];return t}(t,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=n.length,o=r.length-1;o>=0;o--){var s=r[o],a=e.offsetAt(s.range.start),c=e.offsetAt(s.range.end);if(!(c<=i))throw new Error("Overlapping edit");n=n.substring(0,a)+s.newText+n.substring(c,n.length),i=a}return n}}(Q||(Q={}));var ee,te=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var i=t.charAt(r);n="\r"===i||"\n"===i,"\r"===i&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,i=t.length;if(0===i)return r.create(0,e);for(;n<i;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o+1}var s=n-1;return r.create(s,e-t[s])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(ee||(ee={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(208),i=n(12),o=n(10),s=n(209);t.ImplementationRequest=s.ImplementationRequest;const a=n(210);t.TypeDefinitionRequest=a.TypeDefinitionRequest;const c=n(211);t.WorkspaceFoldersRequest=c.WorkspaceFoldersRequest,t.DidChangeWorkspaceFoldersNotification=c.DidChangeWorkspaceFoldersNotification;const u=n(212);t.ConfigurationRequest=u.ConfigurationRequest;const l=n(213);t.DocumentColorRequest=l.DocumentColorRequest,t.ColorPresentationRequest=l.ColorPresentationRequest;const f=n(214);t.FoldingRangeRequest=f.FoldingRangeRequest;const d=n(215);t.DeclarationRequest=d.DeclarationRequest;const h=n(216);t.SelectionRangeRequest=h.SelectionRangeRequest;const p=n(217);var m,g;t.WorkDoneProgress=p.WorkDoneProgress,t.WorkDoneProgressCreateRequest=p.WorkDoneProgressCreateRequest,t.WorkDoneProgressCancelNotification=p.WorkDoneProgressCancelNotification,function(e){e.is=function(e){const t=e;return r.string(t.language)||r.string(t.scheme)||r.string(t.pattern)}}(m=t.DocumentFilter||(t.DocumentFilter={})),function(e){e.is=function(e){if(!Array.isArray(e))return!1;for(let t of e)if(!r.string(t)&&!m.is(t))return!1;return!0}}(g=t.DocumentSelector||(t.DocumentSelector={})),function(e){e.type=new o.ProtocolRequestType("client/registerCapability")}(t.RegistrationRequest||(t.RegistrationRequest={})),function(e){e.type=new o.ProtocolRequestType("client/unregisterCapability")}(t.UnregistrationRequest||(t.UnregistrationRequest={})),function(e){e.Create="create",e.Rename="rename",e.Delete="delete"}(t.ResourceOperationKind||(t.ResourceOperationKind={})),function(e){e.Abort="abort",e.Transactional="transactional",e.TextOnlyTransactional="textOnlyTransactional",e.Undo="undo"}(t.FailureHandlingKind||(t.FailureHandlingKind={})),function(e){e.hasId=function(e){const t=e;return t&&r.string(t.id)&&t.id.length>0}}(t.StaticRegistrationOptions||(t.StaticRegistrationOptions={})),function(e){e.is=function(e){const t=e;return t&&(null===t.documentSelector||g.is(t.documentSelector))}}(t.TextDocumentRegistrationOptions||(t.TextDocumentRegistrationOptions={})),function(e){e.is=function(e){const t=e;return r.objectLiteral(t)&&(void 0===t.workDoneProgress||r.boolean(t.workDoneProgress))},e.hasWorkDoneProgress=function(e){const t=e;return t&&r.boolean(t.workDoneProgress)}}(t.WorkDoneProgressOptions||(t.WorkDoneProgressOptions={})),function(e){e.type=new o.ProtocolRequestType("initialize")}(t.InitializeRequest||(t.InitializeRequest={})),function(e){e.unknownProtocolVersion=1}(t.InitializeError||(t.InitializeError={})),function(e){e.type=new o.ProtocolNotificationType("initialized")}(t.InitializedNotification||(t.InitializedNotification={})),function(e){e.type=new o.ProtocolRequestType0("shutdown")}(t.ShutdownRequest||(t.ShutdownRequest={})),function(e){e.type=new o.ProtocolNotificationType0("exit")}(t.ExitNotification||(t.ExitNotification={})),function(e){e.type=new o.ProtocolNotificationType("workspace/didChangeConfiguration")}(t.DidChangeConfigurationNotification||(t.DidChangeConfigurationNotification={})),function(e){e.Error=1,e.Warning=2,e.Info=3,e.Log=4}(t.MessageType||(t.MessageType={})),function(e){e.type=new o.ProtocolNotificationType("window/showMessage")}(t.ShowMessageNotification||(t.ShowMessageNotification={})),function(e){e.type=new o.ProtocolRequestType("window/showMessageRequest")}(t.ShowMessageRequest||(t.ShowMessageRequest={})),function(e){e.type=new o.ProtocolNotificationType("window/logMessage")}(t.LogMessageNotification||(t.LogMessageNotification={})),function(e){e.type=new o.ProtocolNotificationType("telemetry/event")}(t.TelemetryEventNotification||(t.TelemetryEventNotification={})),function(e){e.None=0,e.Full=1,e.Incremental=2}(t.TextDocumentSyncKind||(t.TextDocumentSyncKind={})),function(e){e.method="textDocument/didOpen",e.type=new o.ProtocolNotificationType(e.method)}(t.DidOpenTextDocumentNotification||(t.DidOpenTextDocumentNotification={})),function(e){e.method="textDocument/didChange",e.type=new o.ProtocolNotificationType(e.method)}(t.DidChangeTextDocumentNotification||(t.DidChangeTextDocumentNotification={})),function(e){e.method="textDocument/didClose",e.type=new o.ProtocolNotificationType(e.method)}(t.DidCloseTextDocumentNotification||(t.DidCloseTextDocumentNotification={})),function(e){e.method="textDocument/didSave",e.type=new o.ProtocolNotificationType(e.method)}(t.DidSaveTextDocumentNotification||(t.DidSaveTextDocumentNotification={})),function(e){e.Manual=1,e.AfterDelay=2,e.FocusOut=3}(t.TextDocumentSaveReason||(t.TextDocumentSaveReason={})),function(e){e.method="textDocument/willSave",e.type=new o.ProtocolNotificationType(e.method)}(t.WillSaveTextDocumentNotification||(t.WillSaveTextDocumentNotification={})),function(e){e.method="textDocument/willSaveWaitUntil",e.type=new o.ProtocolRequestType(e.method)}(t.WillSaveTextDocumentWaitUntilRequest||(t.WillSaveTextDocumentWaitUntilRequest={})),function(e){e.type=new o.ProtocolNotificationType("workspace/didChangeWatchedFiles")}(t.DidChangeWatchedFilesNotification||(t.DidChangeWatchedFilesNotification={})),function(e){e.Created=1,e.Changed=2,e.Deleted=3}(t.FileChangeType||(t.FileChangeType={})),function(e){e.Create=1,e.Change=2,e.Delete=4}(t.WatchKind||(t.WatchKind={})),function(e){e.type=new o.ProtocolNotificationType("textDocument/publishDiagnostics")}(t.PublishDiagnosticsNotification||(t.PublishDiagnosticsNotification={})),function(e){e.Invoked=1,e.TriggerCharacter=2,e.TriggerForIncompleteCompletions=3}(t.CompletionTriggerKind||(t.CompletionTriggerKind={})),function(e){e.method="textDocument/completion",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.CompletionRequest||(t.CompletionRequest={})),function(e){e.method="completionItem/resolve",e.type=new o.ProtocolRequestType(e.method)}(t.CompletionResolveRequest||(t.CompletionResolveRequest={})),function(e){e.method="textDocument/hover",e.type=new o.ProtocolRequestType(e.method)}(t.HoverRequest||(t.HoverRequest={})),function(e){e.Invoked=1,e.TriggerCharacter=2,e.ContentChange=3}(t.SignatureHelpTriggerKind||(t.SignatureHelpTriggerKind={})),function(e){e.method="textDocument/signatureHelp",e.type=new o.ProtocolRequestType(e.method)}(t.SignatureHelpRequest||(t.SignatureHelpRequest={})),function(e){e.method="textDocument/definition",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.DefinitionRequest||(t.DefinitionRequest={})),function(e){e.method="textDocument/references",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.ReferencesRequest||(t.ReferencesRequest={})),function(e){e.method="textDocument/documentHighlight",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.DocumentHighlightRequest||(t.DocumentHighlightRequest={})),function(e){e.method="textDocument/documentSymbol",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.DocumentSymbolRequest||(t.DocumentSymbolRequest={})),function(e){e.method="textDocument/codeAction",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.CodeActionRequest||(t.CodeActionRequest={})),function(e){e.method="workspace/symbol",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.WorkspaceSymbolRequest||(t.WorkspaceSymbolRequest={})),function(e){e.type=new o.ProtocolRequestType("textDocument/codeLens"),e.resultType=new i.ProgressType}(t.CodeLensRequest||(t.CodeLensRequest={})),function(e){e.type=new o.ProtocolRequestType("codeLens/resolve")}(t.CodeLensResolveRequest||(t.CodeLensResolveRequest={})),function(e){e.method="textDocument/documentLink",e.type=new o.ProtocolRequestType(e.method),e.resultType=new i.ProgressType}(t.DocumentLinkRequest||(t.DocumentLinkRequest={})),function(e){e.type=new o.ProtocolRequestType("documentLink/resolve")}(t.DocumentLinkResolveRequest||(t.DocumentLinkResolveRequest={})),function(e){e.method="textDocument/formatting",e.type=new o.ProtocolRequestType(e.method)}(t.DocumentFormattingRequest||(t.DocumentFormattingRequest={})),function(e){e.method="textDocument/rangeFormatting",e.type=new o.ProtocolRequestType(e.method)}(t.DocumentRangeFormattingRequest||(t.DocumentRangeFormattingRequest={})),function(e){e.method="textDocument/onTypeFormatting",e.type=new o.ProtocolRequestType(e.method)}(t.DocumentOnTypeFormattingRequest||(t.DocumentOnTypeFormattingRequest={})),function(e){e.method="textDocument/rename",e.type=new o.ProtocolRequestType(e.method)}(t.RenameRequest||(t.RenameRequest={})),function(e){e.method="textDocument/prepareRename",e.type=new o.ProtocolRequestType(e.method)}(t.PrepareRenameRequest||(t.PrepareRenameRequest={})),function(e){e.type=new o.ProtocolRequestType("workspace/executeCommand")}(t.ExecuteCommandRequest||(t.ExecuteCommandRequest={})),function(e){e.type=new o.ProtocolRequestType("workspace/applyEdit")}(t.ApplyWorkspaceEditRequest||(t.ApplyWorkspaceEditRequest={}))},function(e,t,n){"use strict";function r(e){return"string"==typeof e||e instanceof String}function i(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.boolean=function(e){return!0===e||!1===e},t.string=r,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=function(e){return"function"==typeof e},t.array=i,t.stringArray=function(e){return i(e)&&e.every(e=>r(e))},t.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)},t.objectLiteral=function(e){return null!==e&&"object"==typeof e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.method="textDocument/implementation",e.type=new i.ProtocolRequestType(e.method),e.resultType=new r.ProgressType}(t.ImplementationRequest||(t.ImplementationRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.method="textDocument/typeDefinition",e.type=new i.ProtocolRequestType(e.method),e.resultType=new r.ProgressType}(t.TypeDefinitionRequest||(t.TypeDefinitionRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(10);!function(e){e.type=new r.ProtocolRequestType0("workspace/workspaceFolders")}(t.WorkspaceFoldersRequest||(t.WorkspaceFoldersRequest={})),function(e){e.type=new r.ProtocolNotificationType("workspace/didChangeWorkspaceFolders")}(t.DidChangeWorkspaceFoldersNotification||(t.DidChangeWorkspaceFoldersNotification={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(10);!function(e){e.type=new r.ProtocolRequestType("workspace/configuration")}(t.ConfigurationRequest||(t.ConfigurationRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.method="textDocument/documentColor",e.type=new i.ProtocolRequestType(e.method),e.resultType=new r.ProgressType}(t.DocumentColorRequest||(t.DocumentColorRequest={})),function(e){e.type=new i.ProtocolRequestType("textDocument/colorPresentation")}(t.ColorPresentationRequest||(t.ColorPresentationRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(t.FoldingRangeKind||(t.FoldingRangeKind={})),function(e){e.method="textDocument/foldingRange",e.type=new i.ProtocolRequestType(e.method),e.resultType=new r.ProgressType}(t.FoldingRangeRequest||(t.FoldingRangeRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.method="textDocument/declaration",e.type=new i.ProtocolRequestType(e.method),e.resultType=new r.ProgressType}(t.DeclarationRequest||(t.DeclarationRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.method="textDocument/selectionRange",e.type=new i.ProtocolRequestType(e.method),e.resultType=new r.ProgressType}(t.SelectionRangeRequest||(t.SelectionRangeRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(12),i=n(10);!function(e){e.type=new r.ProgressType}(t.WorkDoneProgress||(t.WorkDoneProgress={})),function(e){e.type=new i.ProtocolRequestType("window/workDoneProgress/create")}(t.WorkDoneProgressCreateRequest||(t.WorkDoneProgressCreateRequest={})),function(e){e.type=new i.ProtocolNotificationType("window/workDoneProgress/cancel")}(t.WorkDoneProgressCancelNotification||(t.WorkDoneProgressCancelNotification={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(10);!function(e){e.method="textDocument/prepareCallHierarchy",e.type=new r.ProtocolRequestType(e.method)}(t.CallHierarchyPrepareRequest||(t.CallHierarchyPrepareRequest={})),function(e){e.method="callHierarchy/incomingCalls",e.type=new r.ProtocolRequestType(e.method)}(t.CallHierarchyIncomingCallsRequest||(t.CallHierarchyIncomingCallsRequest={})),function(e){e.method="callHierarchy/outgoingCalls",e.type=new r.ProtocolRequestType(e.method)}(t.CallHierarchyOutgoingCallsRequest||(t.CallHierarchyOutgoingCallsRequest={}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(10);!function(e){e.comment="comment",e.keyword="keyword",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.namespace="namespace",e.type="type",e.struct="struct",e.class="class",e.interface="interface",e.enum="enum",e.typeParameter="typeParameter",e.function="function",e.member="member",e.property="property",e.macro="macro",e.variable="variable",e.parameter="parameter",e.label="label"}(t.SemanticTokenTypes||(t.SemanticTokenTypes={})),function(e){e.documentation="documentation",e.declaration="declaration",e.definition="definition",e.reference="reference",e.static="static",e.abstract="abstract",e.deprecated="deprecated",e.async="async",e.volatile="volatile",e.readonly="readonly"}(t.SemanticTokenModifiers||(t.SemanticTokenModifiers={})),function(e){e.is=function(e){const t=e;return void 0!==t&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(t.SemanticTokens||(t.SemanticTokens={})),function(e){e.method="textDocument/semanticTokens",e.type=new r.ProtocolRequestType(e.method)}(t.SemanticTokensRequest||(t.SemanticTokensRequest={})),function(e){e.method="textDocument/semanticTokens/edits",e.type=new r.ProtocolRequestType(e.method)}(t.SemanticTokensEditsRequest||(t.SemanticTokensEditsRequest={})),function(e){e.method="textDocument/semanticTokens/range",e.type=new r.ProtocolRequestType(e.method)}(t.SemanticTokensRangeRequest||(t.SemanticTokensRangeRequest={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))},i=this&&this.__asyncValues||function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e="function"==typeof __values?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){!function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)}(r,i,(t=e[n](t)).done,t.value)}))}}};Object.defineProperty(t,"__esModule",{value:!0}),t.filterObjectMethodsOnCompletions=t.getParamType=t.filterParsedLanguageFeatures=void 0;const o=n(19);t.filterParsedLanguageFeatures=(e,n,s)=>{var a,c;return r(void 0,void 0,void 0,(function*(){var r,u;try{if(e.length>1){let o=[],l=0;try{for(a=i(e);!(c=yield a.next()).done;){const e=c.value;if(0===l)o=yield t.filterObjectMethodsOnCompletions(e,n,s);else{let r=[];"HOVER"===s?void 0!==o.methods&&(r=o.methods.filter(t=>t.label===e),o[0]=o.methods):r=o.filter(t=>t.label===e);const i=t.getParamType(r);o=i?yield t.filterObjectMethodsOnCompletions(i,n,s):r[0]}l++}}catch(e){r={error:e}}finally{try{c&&!c.done&&(u=a.return)&&(yield u.call(a))}finally{if(r)throw r.error}}return o}return t.filterObjectMethodsOnCompletions(e[0],n,s)}catch(e){o.default.error(e.message+" [filterParsedLanguageFeatures] ")}}))},t.getParamType=e=>{const t=["Byte","Boolean","Integer","Long","Currency","Single","Double","Decimal","Date","String","Object","Variant"];if(void 0!==e&&e.length>0){let n=/(\()(.*?)(?=\s*)\)/gi;const r=e[0].detail.replace(n,"").match(/(?<= As )\w+/g);if(null!==r)return 0===t.filter(e=>e===r[0]).length?r[0]:null}return null},t.filterObjectMethodsOnCompletions=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const r=t.filter(t=>t.label===e);return void 0===r[0]?[]:"AUTOCOMPLETE"===n?r[0].methods:"HOVER"===n?r[0]:void 0}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getCompletionItems=t.textDocument=void 0;const r=n(56),i=n(46),o=n(488),s=n(489),a=n(52);t.textDocument=null,t.getCompletionItems=e=>{t.textDocument=e;const n=e.getText(),r=[],s=c(n);let l="",f="",d="";return i.split(n).forEach((t,n)=>{const i=/^ *\'/.test(t);i&&!/^ *\' *namespace/gim.test(t)&&(f+=t+"\r\n");const s=f.match(a.RegexPatterns.comments);if(s&&(l=s[0],f=""),0===t.trim().length&&""===d&&(d=l),!i&&t.trim().length>0){const i={start:{line:n,character:t.length},end:{line:n,character:0}},s=o.clearComment(l),a=u(t);if(a){const t=a.map(t=>Object.assign(Object.assign({},t),{range:i,documentation:s,uri:e.uri}));r.push(...t)}l="",f=""}}),[{label:s.vbName,kind:s.vbKind,detail:s.vbName,documentation:o.clearComment(d),uri:e.uri,methods:r}]};const c=e=>{const t=e.match(/^ *Attribute *VB_Name *= * (.*)/gim),n=e.match(/(?<=class )(.*?)(?=\s*\()/gi);let i="",o="",s=r.CompletionItemKind.Module;return t?(i=t[0].split("=")[1].trim().replace('"',"").replace('"',""),o=t[0].trim(),s=r.CompletionItemKind.Module):n&&(i=n[0],s=r.CompletionItemKind.Class),{vbName:i,vbDetails:o,vbKind:s}},u=e=>{let t=null;return s.regexCompletionItems.forEach(n=>{if(n.detail(e)){const r=n.label(e);return t=[{label:r,detail:e,kind:n.kind()}],t}}),t}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.connection=t.hasConfigurationCapability=void 0;const i=n(56),o=n(299),s=n(486),a=n(92),c=n(492),u=n(493),l=n(19),f=n(494),d=n(495),h=n(498),p=n(499);t.hasConfigurationCapability=!1,t.connection=i.createConnection(i.ProposedFeatures.all);let m=new i.TextDocuments(o.TextDocument);const g=()=>{t.hasConfigurationCapability&&t.connection.client.register(i.DidChangeConfigurationNotification.type,void 0)};t.connection.onInitialize(e=>f.onInitializeConnectionHandler(e,t.hasConfigurationCapability)),t.connection.onInitialized(e=>r(void 0,void 0,void 0,(function*(){yield d.onInitializedConnection(e,t.connection,g)})));const y={maxNumberOfProblems:1e3,commitCharacters:".",trace:{server:"verbose"}};let v=y,b=new Map;t.connection.onDidChangeConfiguration(e=>{t.hasConfigurationCapability?b.clear():v=e.settings.languageServerVBA||y}),m.onDidClose(e=>{b.delete(e.document.uri)}),t.connection.onDidChangeWatchedFiles(e=>{l.default.debug("We received an file change event")}),m.onDidSave(e=>r(void 0,void 0,void 0,(function*(){yield h.onDocumentSave(e)}))),m.onDidClose(e=>{t.connection.workspace.getWorkspaceFolders().then(e=>r(void 0,void 0,void 0,(function*(){yield a.InitializeLanguageServerFeatures(e)})))}),m.onDidChangeContent(e=>r(void 0,void 0,void 0,(function*(){let n=e.document;t.connection.onCompletion(e=>r(void 0,void 0,void 0,(function*(){return yield s.default(n,e)})))}))),t.connection.onCompletionResolve(e=>e),t.connection.onNotification(e=>r(void 0,void 0,void 0,(function*(){t.connection.workspace.getWorkspaceFolders().then(t=>r(void 0,void 0,void 0,(function*(){u.ManagerReceiveNotificationService(e,t)})))}))),t.connection.onDocumentFormatting(e=>r(void 0,void 0,void 0,(function*(){return p.onDocumentFormatter(t.connection,m,e)})));let _=null;t.connection.onHover(({textDocument:e,position:t})=>r(void 0,void 0,void 0,(function*(){let n=m.get(e.uri);return n?(_=yield c.onHoverService(n,t),void 0!==_?_.payload:null):null}))),t.connection.onDefinition(e=>r(void 0,void 0,void 0,(function*(){return void 0!==_&&void 0!==_.uri?{uri:_.uri,range:_.range}:null}))),m.listen(t.connection),t.connection.listen()},function(e,t,n){"use strict";function r(e){return"string"==typeof e||e instanceof String}function i(e){return"function"==typeof e}function o(e){return Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.boolean=function(e){return!0===e||!1===e},t.string=r,t.number=function(e){return"number"==typeof e||e instanceof Number},t.error=function(e){return e instanceof Error},t.func=i,t.array=o,t.stringArray=function(e){return o(e)&&e.every(e=>r(e))},t.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)},t.thenable=function(e){return e&&i(e.then)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r{constructor(e){this._value=e}asHex(){return this._value}equals(e){return this.asHex()===e.asHex()}}class i extends r{constructor(){super([i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),"-",i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),"-","4",i._randomHex(),i._randomHex(),i._randomHex(),"-",i._oneOf(i._timeHighBits),i._randomHex(),i._randomHex(),i._randomHex(),"-",i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex(),i._randomHex()].join(""))}static _oneOf(e){return e[Math.floor(e.length*Math.random())]}static _randomHex(){return i._oneOf(i._chars)}}function o(){return new i}i._chars=["0","1","2","3","4","5","6","6","7","8","9","a","b","c","d","e","f"],i._timeHighBits=["8","9","a","b"],t.empty=new r("00000000-0000-0000-0000-000000000000"),t.v4=o;const s=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function a(e){return s.test(e)}t.isUUID=a,t.parse=function(e){if(!a(e))throw new Error("invalid uuid");return new r(e)},t.generateUuid=function(){return o().asHex()}},function(e,t,n){"use strict";n.r(t),n.d(t,"TextDocument",(function(){return r}));var r,i=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(t,n){for(var r=0,i=t;r<i.length;r++){var a=i[r];if(e.isIncremental(a)){var c=s(a.range),u=this.offsetAt(c.start),l=this.offsetAt(c.end);this._content=this._content.substring(0,u)+a.text+this._content.substring(l,this._content.length);var f=Math.max(c.start.line,0),d=Math.max(c.end.line,0),h=this._lineOffsets,p=o(a.text,!1,u);if(d-f===p.length)for(var m=0,g=p.length;m<g;m++)h[m+f+1]=p[m];else p.length<1e4?h.splice.apply(h,[f+1,d-f].concat(p)):this._lineOffsets=h=h.slice(0,f+1).concat(p,h.slice(d+1));var y=a.text.length-(l-u);if(0!==y)for(m=f+1+p.length,g=h.length;m<g;m++)h[m]=h[m]+y}else{if(!e.isFull(a))throw new Error("Unknown change event received");this._content=a.text,this._lineOffsets=void 0}}this._version=n},e.prototype.getLineOffsets=function(){return void 0===this._lineOffsets&&(this._lineOffsets=o(this._content,!0)),this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){var i=Math.floor((n+r)/2);t[i]>e?r=i:n=i+1}var o=n-1;return{line:o,character:e-t[o]}},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e.isIncremental=function(e){var t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)},e.isFull=function(e){var t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength},e}();function o(e,t,n){void 0===n&&(n=0);for(var r=t?[n]:[],i=0;i<e.length;i++){var o=e.charCodeAt(i);13!==o&&10!==o||(13===o&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,r.push(n+i+1))}return r}function s(e){var t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function a(e){var t=s(e.range);return t!==e.range?{newText:e.newText,range:t}:e}!function(e){e.create=function(e,t,n,r){return new i(e,t,n,r)},e.update=function(e,t,n){if(e instanceof i)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){for(var n=e.getText(),r=0,i=[],o=0,s=function e(t,n){if(t.length<=1)return t;var r=t.length/2|0,i=t.slice(0,r),o=t.slice(r);e(i,n),e(o,n);for(var s=0,a=0,c=0;s<i.length&&a<o.length;){var u=n(i[s],o[a]);t[c++]=u<=0?i[s++]:o[a++]}for(;s<i.length;)t[c++]=i[s++];for(;a<o.length;)t[c++]=o[a++];return t}(t.map(a),(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n}));o<s.length;o++){var c=s[o],u=e.offsetAt(c.range.start);if(u<r)throw new Error("Overlapping edit");u>r&&i.push(n.substring(r,u)),c.newText.length&&i.push(c.newText),r=e.offsetAt(c.range.end)}return i.push(n.substr(r)),i.join("")}}(r||(r={}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getAllAppParsedLanguageFeatures=void 0;const i=n(487),o=n(0),s=n(3),a=n(221),{readFile:c}=n(1).promises;t.getAllAppParsedLanguageFeatures=(e,t)=>r(void 0,void 0,void 0,(function*(){const n=yield u(),r=yield i.getFunctionScopeParamsForCompletionItems(e,t,n),o=a.getCompletionItems(e);let s=[];return o&&(s=o[0].methods),[...n,...r,...s]}));const u=()=>r(void 0,void 0,void 0,(function*(){const e=s.tmpdir(),t=o.join(e,"\\auto.json"),n=yield c(t,"ascii");return JSON.parse(n)}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.filterVBAFiles=void 0;const r=n(0);t.filterVBAFiles=e=>e.reduce((e,t)=>(".bas"!==r.extname(t)&&".cls"!==r.extname(t)&&".frm"!==r.extname(t)&&".vb"!==r.extname(t)||(e=[...e,t]),e),[])},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.createDocument=void 0;const i=n(37),{readFile:o,writeFile:s}=n(1).promises,a=n(299);t.createDocument=e=>r(void 0,void 0,void 0,(function*(){try{const t=[];for(const n of e){const e=yield o(n),r=i.URI.file(n).toString();let s=e.toString();const c=a.TextDocument.create(r,"vb",1,s);t.push(c)}return t}catch(e){console.log(e)}}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.makeCodeForDebug=void 0;const i=n(3),o=n(0),s=n(1),a=n(46),c=n(52),u=n(55);t.makeCodeForDebug=e=>r(void 0,void 0,void 0,(function*(){const t=a.split(e.getText());let n=d(t);const r=h();yield l(e,r,n)}));const l=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const r=u.fileURLToPath(e.uri),i=o.basename(r);f(r)&&s.writeFile(o.join(t,i),n,e=>{if(e)throw new Error("Error: File for Debug not Created")})})),f=e=>{let t=!1,n=!1;const r=o.extname(e);return t=e.includes("xvba_modules"),n=e.includes("xvba_unit_test"),!(t||n||".bas"!==r&&".cls"!==r&&".frm"!==r)},d=e=>{let t="";const n=new RegExp(c.RegexPatterns.function),r=new RegExp(c.RegexPatterns.functionEnd);let i=!1;return e.forEach((e,o)=>{let s=n.test(e);s&&(i=!0),!i||s||e.includes("Case")||e.includes(":")||""===e.trim()?t=e.includes(":")?t+"\r\n"+e.trim():t+"\r\n"+e:(t=t+"\r\n"+(o+1)+":  "+e,r.test(e)&&(i=!1))}),t},h=()=>{const e=o.join(i.tmpdir(),"xvba_code_debug_parsed");return s.existsSync(e)||s.mkdir(e,e=>{if(e)throw new Error("Error: Folder xvba_code_debug_parsed not Created")}),e}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9),i=n(297);t.ConfigurationFeature=e=>class extends e{getConfiguration(e){return e?i.string(e)?this._getConfiguration({section:e}):this._getConfiguration(e):this._getConfiguration({})}_getConfiguration(e){let t={items:Array.isArray(e)?e:[e]};return this.connection.sendRequest(r.ConfigurationRequest.type,t).then(t=>Array.isArray(e)?t:t[0])}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9);t.WorkspaceFoldersFeature=e=>class extends e{initialize(e){let t=e.workspace;t&&t.workspaceFolders&&(this._onDidChangeWorkspaceFolders=new r.Emitter,this.connection.onNotification(r.DidChangeWorkspaceFoldersNotification.type,e=>{this._onDidChangeWorkspaceFolders.fire(e.event)}))}getWorkspaceFolders(){return this.connection.sendRequest(r.WorkspaceFoldersRequest.type)}get onDidChangeWorkspaceFolders(){if(!this._onDidChangeWorkspaceFolders)throw new Error("Client doesn't support sending workspace folder change events.");return this._unregistration||(this._unregistration=this.connection.client.register(r.DidChangeWorkspaceFoldersNotification.type)),this._onDidChangeWorkspaceFolders.event}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9),i=n(298);class o{constructor(e,t){this._connection=e,this._token=t,o.Instances.set(this._token,this),this._source=new r.CancellationTokenSource}get token(){return this._source.token}begin(e,t,n,i){let o={kind:"begin",title:e,percentage:t,message:n,cancellable:i};this._connection.sendProgress(r.WorkDoneProgress.type,this._token,o)}report(e,t){let n={kind:"report"};"number"==typeof e?(n.percentage=e,void 0!==t&&(n.message=t)):n.message=e,this._connection.sendProgress(r.WorkDoneProgress.type,this._token,n)}done(){o.Instances.delete(this._token),this._source.dispose(),this._connection.sendProgress(r.WorkDoneProgress.type,this._token,{kind:"end"})}cancel(){this._source.cancel()}}o.Instances=new Map;class s{constructor(){this._source=new r.CancellationTokenSource}get token(){return this._source.token}begin(){}report(){}done(){}}var a;t.attachWorkDone=function(e,t){if(void 0===t||void 0===t.workDoneToken)return new s;const n=t.workDoneToken;return delete t.workDoneToken,new o(e,n)},t.ProgressFeature=e=>class extends e{initialize(e){var t;!0===(null===(t=null==e?void 0:e.window)||void 0===t?void 0:t.workDoneProgress)&&(this._progressSupported=!0,this.connection.onNotification(r.WorkDoneProgressCancelNotification.type,e=>{let t=o.Instances.get(e.token);void 0!==t&&t.cancel()}))}attachWorkDoneProgress(e){return void 0===e?new s:new o(this.connection,e)}createWorkDoneProgress(){if(this._progressSupported){const e=i.generateUuid();return this.connection.sendRequest(r.WorkDoneProgressCreateRequest.type,{token:e}).then(()=>new o(this.connection,e))}return Promise.resolve(new s)}},function(e){e.type=new r.ProgressType}(a||(a={}));class c{constructor(e,t){this._connection=e,this._token=t}report(e){this._connection.sendProgress(a.type,this._token,e)}}t.attachPartialResult=function(e,t){if(void 0===t||void 0===t.partialResultToken)return;const n=t.partialResultToken;return delete t.partialResultToken,new c(e,n)}},function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0});const r=n(55),i=n(0),o=n(1),s=n(41);function a(){return"win32"===process.platform}function c(e,t,n,r){const a=["var p = process;","p.on('message',function(m){","if(m.c==='e'){","p.exit(0);","}","else if(m.c==='rs'){","try{","var r=require.resolve(m.a);","p.send({c:'r',s:true,r:r});","}","catch(err){","p.send({c:'r',s:false});","}","}","});"].join("");return new Promise((c,u)=>{let l=process.env,f=Object.create(null);Object.keys(l).forEach(e=>f[e]=l[e]),t&&o.existsSync(t)&&(f.NODE_PATH?f.NODE_PATH=t+i.delimiter+f.NODE_PATH:f.NODE_PATH=t,r&&r("NODE_PATH value is: "+f.NODE_PATH)),f.ELECTRON_RUN_AS_NODE="1";try{let t=s.fork("",[],{cwd:n,env:f,execArgv:["-e",a]});if(void 0===t.pid)return void u(new Error(`Starting process to resolve node module  ${e} failed`));t.on("error",e=>{u(e)}),t.on("message",n=>{"r"===n.c&&(t.send({c:"e"}),n.s?c(n.r):u(new Error("Failed to resolve module: "+e)))});let r={c:"rs",a:e};t.send(r)}catch(e){u(e)}})}function u(e){let t="npm";const n=Object.create(null);Object.keys(process.env).forEach(e=>n[e]=process.env[e]),n.NO_UPDATE_NOTIFIER="true";const r={encoding:"utf8",env:n};a()&&(t="npm.cmd",r.shell=!0);let o=()=>{};try{process.on("SIGPIPE",o);let n=s.spawnSync(t,["config","get","prefix"],r).stdout;if(!n)return void(e&&e("'npm config get prefix' didn't return a value."));let c=n.trim();return e&&e("'npm config get prefix' value is: "+c),c.length>0?a()?i.join(c,"node_modules"):i.join(c,"lib","node_modules"):void 0}catch(e){return}finally{process.removeListener("SIGPIPE",o)}}var l;t.uriToFilePath=function(e){let t=r.parse(e);if("file:"!==t.protocol||!t.path)return;let n=t.path.split("/");for(var o=0,s=n.length;o<s;o++)n[o]=decodeURIComponent(n[o]);if("win32"===process.platform&&n.length>1){let e=n[0],t=n[1];0===e.length&&t.length>1&&":"===t[1]&&n.shift()}return i.normalize(n.join("/"))},t.resolve=c,t.resolveGlobalNodePath=u,t.resolveGlobalYarnPath=function(e){let t="yarn",n={encoding:"utf8"};a()&&(t="yarn.cmd",n.shell=!0);let r=()=>{};try{process.on("SIGPIPE",r);let o=s.spawnSync(t,["global","dir","--json"],n),a=o.stdout;if(!a)return void(e&&(e("'yarn global dir' didn't return a value."),o.stderr&&e(o.stderr)));let c=a.trim().split(/\r?\n/);for(let t of c)try{let e=JSON.parse(t);if("log"===e.type)return i.join(e.data,"node_modules")}catch(e){}return}catch(e){return}finally{process.removeListener("SIGPIPE",r)}},function(t){let n;function r(){return void 0!==n||(n=!("win32"===process.platform||o.existsSync(e.toUpperCase())&&o.existsSync(e.toLowerCase()))),n}t.isCaseSensitive=r,t.isParent=function(e,t){return r()?0===i.normalize(t).indexOf(i.normalize(e)):0===i.normalize(t).toLowerCase().indexOf(i.normalize(e).toLowerCase())}}(l=t.FileSystem||(t.FileSystem={})),t.resolveModulePath=function(e,t,n,r){return n?(i.isAbsolute(n)||(n=i.join(e,n)),c(t,n,n,r).then(e=>l.isParent(n,e)?e:Promise.reject(new Error(`Failed to load ${t} from node path location.`))).then(void 0,n=>c(t,u(r),e,r))):c(t,u(r),e,r)}}).call(this,"/index.js")},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9);t.CallHierarchyFeature=e=>class extends e{get callHierarchy(){return{onPrepare:e=>{this.connection.onRequest(r.Proposed.CallHierarchyPrepareRequest.type,(t,n)=>e(t,n,this.attachWorkDoneProgress(t),void 0))},onIncomingCalls:e=>{const t=r.Proposed.CallHierarchyIncomingCallsRequest.type;this.connection.onRequest(t,(n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n)))},onOutgoingCalls:e=>{const t=r.Proposed.CallHierarchyOutgoingCallsRequest.type;this.connection.onRequest(t,(n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n)))}}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=n(9);t.SemanticTokensFeature=e=>class extends e{get semanticTokens(){return{on:e=>{const t=r.Proposed.SemanticTokensRequest.type;this.connection.onRequest(t,(n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n)))},onEdits:e=>{const t=r.Proposed.SemanticTokensEditsRequest.type;this.connection.onRequest(t,(n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n)))},onRange:e=>{const t=r.Proposed.SemanticTokensRangeRequest.type;this.connection.onRequest(t,(n,r)=>e(n,r,this.attachWorkDoneProgress(n),this.attachPartialResultProgress(t,n)))}}}},t.SemanticTokensBuilder=class{constructor(){this._prevData=void 0,this.initialize()}initialize(){this._id=Date.now(),this._prevLine=0,this._prevChar=0,this._data=[],this._dataLen=0}push(e,t,n,r,i){let o=e,s=t;this._dataLen>0&&(o-=this._prevLine,0===o&&(s-=this._prevChar)),this._data[this._dataLen++]=o,this._data[this._dataLen++]=s,this._data[this._dataLen++]=n,this._data[this._dataLen++]=r,this._data[this._dataLen++]=i,this._prevLine=e,this._prevChar=t}get id(){return this._id.toString()}previousResult(e){this.id===e&&(this._prevData=this._data),this.initialize()}build(){return this._prevData=void 0,{resultId:this.id,data:this._data}}canBuildEdits(){return void 0!==this._prevData}buildEdits(){if(void 0!==this._prevData){const e=this._prevData.length,t=this._data.length;let n=0;for(;n<t&&n<e&&this._prevData[n]===this._data[n];)n++;if(n<t&&n<e){let r=0;for(;r<t&&r<e&&this._prevData[e-1-r]===this._data[t-1-r];)r++;const i=this._data.slice(n,t-r);return{resultId:this.id,edits:[{start:n,deleteCount:e-r-n,data:i}]}}return n<t?{resultId:this.id,edits:[{start:n,deleteCount:0,data:this._data.slice(n)}]}:n<e?{resultId:this.id,edits:[{start:n,deleteCount:e-n}]}:{resultId:this.id,edits:[]}}return this.build()}}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const i=n(220),o=n(300);t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const n=yield o.getAllAppParsedLanguageFeatures(e,t);let r={start:{line:t.position.line,character:t.position.character},end:{line:t.position.line,character:0}},s=e.getText(r).replace(/(\()(.*?)(?=\s*)\)/gi,"").replace(/^ *Call */gi,"");if(!s.endsWith("."))return n;{const e=s.match(/([a-z._$-]*)\./gi);if(e){const t=e[(null==e?void 0:e.length)-1].match(/\w+/gi);return yield i.filterParsedLanguageFeatures(t||[],n,"AUTOCOMPLETE")}}}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getFunctionScopeParamsForCompletionItems=void 0;const i=n(56),o=n(220);t.getFunctionScopeParamsForCompletionItems=(e,t,n)=>r(void 0,void 0,void 0,(function*(){let r=t.position.line,i=!1;const o=[];for(;r&&!i;){let t={start:{line:r,character:1e4},end:{line:r,character:0}},a=e.getText(t);const c=yield s(e,t,n);c&&o.push(c);const u=new RegExp(/(^.* +)(?=sub|function)(.*?)(\))/,"gmi").test(a),l=new RegExp(/( *end +)(sub|function)/,"gi").test(a);(u||l)&&(i=!0),r--}return o}));const s=(e,t,n)=>r(void 0,void 0,void 0,(function*(){let r=e.getText(t);if(new RegExp(/^( *?Dim +.*AS +.*\n?\r?\s?)/,"gmi").test(r)){const t=r.match(/(?<= *Dim +)(.*?)(?= *As)/gim),s={label:null!==t?t[0]:"",kind:i.CompletionItemKind.Variable,detail:r,documentation:"",uri:e.uri,methods:[]},a=o.getParamType([s]);if(a){const e=yield o.filterObjectMethodsOnCompletions(a,n,"AUTOCOMPLETE");return Object.assign(Object.assign({},s),{methods:e,kind:i.CompletionItemKind.Class})}return Object.assign({},s)}}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clearComment=void 0;const r=n(46);t.clearComment=e=>{let t="";if(e.includes("@ignore-comment"))return"";const n=r.split(e);return n.length>1&&n.forEach(e=>{e.includes("@module-comment")||e.includes("@class-comment")||(t+=e.trim()+"\r\n")}),t.replace(/^ *\'\/\*\n?\r?\s?/gim,"").replace(/^ *\'\*\/\n?\r?\s?/gim,"").replace(/^ *\'/gim,"")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.regexCompletionItems=void 0;const r=n(56),i=n(0),o=n(37),s=n(221);t.regexCompletionItems=[{label:e=>c(e,/(?<=sub |function )(.*?)(?=\s*\()/i),detail:e=>e.match(/(?<=sub |function )(.*?)(?=\s*\()/i),kind:()=>a()},{label:e=>u(e),detail:e=>e.match(/(?<= *public +Property +)(.*?)(?= *as)/i),kind:()=>r.CompletionItemKind.Property},{label:e=>c(e,/(?<=Event )(.*?)(?=\s*\()/i),detail:e=>e.match(/(?<= *public +Event +)(.*?)(?= *\()/i),kind:()=>r.CompletionItemKind.Event}];const a=()=>{if(s.textDocument){const e=o.URI.parse(s.textDocument.uri),t=o.uriToFsPath(e,!1);if(".cls"===i.extname(t))return r.CompletionItemKind.Method}return r.CompletionItemKind.Function},c=(e,t)=>{const n=e.match(t);return n?n[0]:"Error:Not Found"},u=e=>{let t=e.match(/^( *((Public | Static | Private |) *Property) *(get|) *(\w*)?)/i);return t?t[5]:""}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.documentParseService=void 0;const r=n(37),i=n(0),o=n(221);t.documentParseService=e=>{const t=r.URI.parse(e.uri),n=r.uriToFsPath(t,!1),s=i.extname(n);return".cls"===s||".bas"===s||".frm"===s||".vb"===s?o.getCompletionItems(e):[]}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.updateParsedItem=void 0;const i=n(3),o=n(0),s=n(19),{readFile:a}=n(1).promises;t.updateParsedItem=e=>r(void 0,void 0,void 0,(function*(){try{s.default.debug("Update List off auto-complete items in auto.json");const t=[];let n={};const r=i.tmpdir(),c=o.join(r,"\\auto.json");let u=JSON.parse(yield a(c,"ascii"));return void 0!==u&&u.forEach(r=>{let i=!1;e.forEach(e=>{r.label===e.label&&(n=e,i=!0)}),i?t.push(n):t.push(r)}),t}catch(e){}}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.onHoverService=void 0;const i=n(56),o=n(220),s=n(300);t.onHoverService=(e,t)=>r(void 0,void 0,void 0,(function*(){t.line,t.character,t.line;const n={start:{line:t.line,character:2e4},end:{line:t.line,character:0}},r=yield s.getAllAppParsedLanguageFeatures(e,{textDocument:e,position:t}),i=e.getText(n),o=yield a(t,i,r);return u(o)}));const a=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const r="([a-z._$-]*)("+c(e,t).text+")",i=new RegExp(r,"gi"),s=t.match(i);if(s){const e=s[0].match(/\w+/gi),t=yield o.filterParsedLanguageFeatures(e||[],n,"HOVER");return void 0!==t?{detail:t.detail||"",documentation:t.documentation||"",uri:t.uri||"",range:t.range||""}:void 0}})),c=(e,t)=>{const n=t.match(/\w+/gim);let r={text:"",start:0,end:0};return null==n||n.forEach(n=>{const i=t.indexOf(n),o=n.length+i;e.character>=i&&e.character<=o&&(r={text:n,start:i,end:o})}),r},u=e=>{let t;return void 0!==e?(e.detail&&(t={kind:i.MarkupKind.Markdown,value:["XVBA","- - -","```vb",e.detail,e.documentation,"```"].join("\n")}),{payload:{contents:t},uri:e.uri,range:e.range}):(t={kind:i.MarkupKind.Markdown,value:["XVBA","- - -","```vb","Definition Not found","```"].join("\n")},{payload:{contents:t},uri:""})}},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.ManagerReceiveNotificationService=void 0;const i=n(92),o=n(19);t.ManagerReceiveNotificationService=(e,t)=>r(void 0,void 0,void 0,(function*(){switch(o.default.debug("Serve Receive Notification: "+e),e){case"update-ls-features":yield i.InitializeLanguageServerFeatures(t)}}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.onInitializeConnectionHandler=void 0;const i=n(56);let o=!1,s=!1;const a=n(19);t.onInitializeConnectionHandler=(e,t)=>r(void 0,void 0,void 0,(function*(){a.default.debug("Initialize Connection");let t=e.capabilities;!t.workspace||t.workspace.configuration,o=!(!t.workspace||!t.workspace.workspaceFolders),s=!!(t.textDocument&&t.textDocument.publishDiagnostics&&t.textDocument.publishDiagnostics.relatedInformation);const n={capabilities:{definitionProvider:!0,documentFormattingProvider:!0,textDocumentSync:i.TextDocumentSyncKind.Full,hoverProvider:!0,completionProvider:{resolveProvider:!0,triggerCharacters:["."]}}};return o&&(n.capabilities.workspace={workspaceFolders:{supported:!0}}),n}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.onInitializedConnection=void 0;const i=n(92),o=n(19),s=n(496),a=n(497);t.onInitializedConnection=(e,t,n)=>r(void 0,void 0,void 0,(function*(){o.default.debug(" - Connection Initialized - "),t.workspace.getWorkspaceFolders().then(e=>r(void 0,void 0,void 0,(function*(){yield i.InitializeLanguageServerFeatures(e),yield s.createAllCodeForDebugEvt(e),yield a.deleteImmediateWindowDataEvt()}))),n()}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.createAllCodeForDebugEvt=void 0;const i=n(55),o=n(29),s=n(3),a=n(0),c=n(301),u=n(302),l=n(303),f=n(19),d=n(36);t.createAllCodeForDebugEvt=e=>r(void 0,void 0,void 0,(function*(){f.default.debug("Start Create Code For VBA Debug");const t=a.join(s.tmpdir(),"xvba_code_debug_parsed");yield o.emptyDir(t);const n=i.fileURLToPath(e[0].uri),r=yield d.getFilesPathsFromFolder(n),h=c.filterVBAFiles(r),p=yield u.createDocument(h);void 0!==p&&p.forEach(e=>{l.makeCodeForDebug(e)})}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.deleteImmediateWindowDataEvt=void 0;const i=n(29),o=n(3),s=n(0);t.deleteImmediateWindowDataEvt=()=>r(void 0,void 0,void 0,(function*(){const e=s.join(o.tmpdir(),"xvba_immediate");yield i.emptyDir(e)}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.onDocumentSave=void 0;const i=n(303),o=n(92),s=n(19);t.onDocumentSave=e=>r(void 0,void 0,void 0,(function*(){s.default.debug("Document Save");let t=e.document;yield o.UpdateLanguageFeaturesOnDocumentChanged(t),i.makeCodeForDebug(t)}))},function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.onDocumentFormatter=void 0;const i=n(500);t.onDocumentFormatter=(e,t,{textDocument:n,options:o})=>r(void 0,void 0,void 0,(function*(){return e.sendNotification("text/formatter"),i.formatter(t,n.uri,o)}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatter=void 0;const r=n(19),i=n(46);let o=2;t.formatter=(e,t,n)=>{try{o=n.tabSize,r.default.debug("Start Formatter Text");const s=e.get(t),a=(null==s?void 0:s.getText())||"",u=i.split(function(e){return e.split("\n").map(e=>function(e){return h.forEach(t=>{const n=new RegExp(`\\b${t}\\b`,"gi");e=e.replace(n,t.replace(/(\w)(\w*)/g,(e,t,n)=>t.toUpperCase()+n.toLowerCase()))}),e}(e)).join("\n")}(a));let l=0;const d=[];let p="";return u.length>0&&u.forEach((e,t)=>{if(""!==e){p=f(e);const n=c(e,l,p);null!==n&&(l=n.indentLevel,d.push({range:{start:{line:t,character:e.length},end:{line:t,character:0}},newText:n.text}))}}),r.default.debug("Finish Formatter Text"),d}catch(e){console.log(e),r.default.debug("Error: on Formatter Text")}};let s={text:"",type:"",tab:"",indentLevel:0},a=[];const c=(e,t,n)=>{let r={indentLevel:t};const i=e.trimLeft();if(""!==i&&""!==n){const{indentLevel:e,tab:o}=u(t,n);if(s={text:i,type:n,tab:o,indentLevel:e},a.push(s),"end"===n){let t=!1,n=a.length-1;for(;n>=0&&!t;)"start"===a[n].type&&(r.text=a[n].tab+i,r.indentLevel=e,t=!0,a[n].type="other"),n--}else r.text=o+i,r.indentLevel=e}if(""===n&&0!==i.length){const{indentLevel:e,tab:n}=u(t,null);r.text=d(i)?" "+i.trim():n+i,r.indentLevel=e}return r},u=(e,t)=>{let n="";const r=" ".repeat(o);if("init"===t)e=1;else if("close"===t)e=0;else if("start"===t)n=r.repeat(e),e++;else if("end"===t)e--,n=r.repeat(e);else if("mid"===t){let t=e-1;n=r.repeat(t)}else if("mid+"===t){let t=e-1;n=r.repeat(t)+" "}else if("same"===t){let e=1;n=r.repeat(e)+""}else n=r.repeat(e);return{indentLevel:e,tab:n}},l=[{type:"function",regex:/(?<=sub |function )(.*?)(?=\s*\()/i,indent:"init"},{type:"If Then",regex:/^ *If/i,indent:"start"},{type:"Else",regex:/^( *Else)/i,indent:"mid"},{type:"End If",regex:/^( *End)(\s)(If)/i,indent:"end"},{type:"End Sub",regex:/^( *End)(\s)(Sub|Function)/i,indent:"close"},{type:"Do While",regex:/^( *Do) +While/i,indent:"start"},{type:"Loop",regex:/^( *Loop)((?!.*).)*$/i,indent:"end"},{type:"For To",regex:/^( *For)(.*)(to)(.*)/i,indent:"start"},{type:"Next",regex:/^( *Next)( .*)/i,indent:"end"},{type:"Do Until",regex:/^( *Do)(\s)(Until)(.*)/i,indent:"start"},{type:"For Each",regex:/^( *For)(\s)(Each)(.*)/i,indent:"start"},{type:"Do",regex:/^( *Do)((?!.*).)*$/i,indent:"start"},{type:"Loop While",regex:/^( *Loop)(\s)(While)(.*)/i,indent:"end"},{type:"Loop Until",regex:/^( *Loop)(\s)(Until)(.*)/i,indent:"end"},{type:"Select Case",regex:/^( *Select)(\s)(Case)(.*)/i,indent:"start"},{type:"Case",regex:/^( *Case)(\s)(.*)/i,indent:"mid+"},{type:"End Select",regex:/^( *End)(\s)(Select)/i,indent:"end"},{type:"While",regex:/^( *While)(\s)(.*)/i,indent:"start"},{type:"Wend",regex:/^( *Wend)/i,indent:"end"},{type:"With",regex:/^ *With/i,indent:"start"},{type:"End With",regex:/^ *End\s*With/i,indent:"end"},{type:"On Error GoTo",regex:/^( *On)(\s)(Error)(\s)(GoTo)(.*)/i,indent:"start"},{type:"Exit Sub",regex:/^( *Exit)(\s)(Sub|Function)/i,indent:"mid+"},{type:"Exit For",regex:/^( *Exit)(\s)(For|Do|While)/i,indent:"mid+"},{type:"Exit Do",regex:/^( *Exit)(\s)(Do)/i,indent:"mid+"},{type:"Exit Select",regex:/^( *Exit)(\s)(Select)/i,indent:"mid+"},{type:"Exit Property",regex:/^( *Exit)(\s)(Property)/i,indent:"mid+"},{type:"Exit Function",regex:/^( *Exit)(\s)(Function)/i,indent:"mid+"},{type:"Exit",regex:/^( *Exit)/i,indent:"mid+"},{type:"OnError GoTo",regex:/^( *On)(\s)(Error)(\s)(GoTo)(.*)/i,indent:"start"},{type:"Resume",regex:/^( *Resume)/i,indent:"start"},{type:"Resume Next",regex:/^( *Resume)(\s)(Next)/i,indent:"mid+"},{type:"Resume Label",regex:/^( *Resume)(\s)(Label)(.*)/i,indent:"mid+"},{type:"GoTo",regex:/^( *GoTo)(.*)/i,indent:"start"},{type:"Static",regex:/^( *Static)(.*)/i,indent:"start"},{type:"Exit Sub",regex:/^( *Exit)(\s)(Sub)/i,indent:"mid+"},{type:"Exit Function",regex:/^( *Exit)(\s)(Function)/i,indent:"mid+"},{type:"Exit Property",regex:/^( *Exit)(\s)(Property)/i,indent:"mid+"},{type:"Exit For",regex:/^( *Exit)(\s)(For)/i,indent:"mid+"},{type:"Exit Do",regex:/^( *Exit)(\s)(Do)/i,indent:"mid+"},{type:"Exit While",regex:/^( *Exit)(\s)(While)/i,indent:"mid+"},{type:"PropertyAssignment",regex:/^\s*(Caption|ClientHeight|ClientLeft|ClientTop|ClientWidth|OleObjectBlob|ShowModal|StartUpPosition)\s*=\s*(.*)$/,indent:"same"}],f=e=>{let t="";return l.forEach(n=>{const r=n.regex.test(e);console.log(r,e),r&&(t=n.indent)}),t},d=e=>{var t;let n=e.trim();const r=n.match(/(\w*)(\:)/i);return!(!r||(null===(t=r[0])||void 0===t?void 0:t.length)!==n.length)},h=["Sub","Function","If","Then","Else","ElseIf","End If","Do","While","Until","Loop","For","To","Next","Select","Case","End Select","With","End With","Exit","Call","On Error","Resume","GoTo","Label","Const","Dim","As","Set","New","Private","Public","Property","Get","Let","ByVal","ByRef","Optional","True","False","And","Or","Not","Xor","Eqv","Imp"]}]);
//# sourceMappingURL=bundle.js.map