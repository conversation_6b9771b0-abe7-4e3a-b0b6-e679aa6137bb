/*! ../../../Actions/CodeDebugAction/createAllCodeForDebugEvt */

/*! ../../../Actions/CodeDebugAction/makeCodeForDebug */

/*! ../../../Actions/ImmediateWindowAction/deleteImmediateWindowDataEvt */

/*! ../../../Helpers/FileSystem/FileSystemHelper */

/*! ../../../Helpers/logger.helper */

/*! ../../../Helpers/regex-patterns */

/*! ../../Helpers/FileSystem/FileSystemHelper */

/*! ../../Helpers/logger.helper */

/*! ../../Helpers/regex-patterns */

/*! ../../lsp/server/helper/create-document.helper */

/*! ../../lsp/server/helper/filter-vba-files-paths.helper */

/*! ../copy */

/*! ../copy-sync */

/*! ../document-parse-features/filter-parsed-language-features */

/*! ../document-parse-features/get-all-app-parsed-language-features */

/*! ../document-parse-features/workspace-files-parse.service */

/*! ../filter-parsed-language-features */

/*! ../fs */

/*! ../getCompletionItems */

/*! ../helper/create-document.helper */

/*! ../helper/filter-vba-files-paths.helper */

/*! ../helper/upade-language-parse-item.helper */

/*! ../mkdirs */

/*! ../output */

/*! ../package.json */

/*! ../path-exists */

/*! ../remove */

/*! ../server */

/*! ../services/formatter.service */

/*! ../util/stat */

/*! ../util/utimes */

/*! ./../../../webpack/buildin/module.js */

/*! ./ConnectionEventsHandler/onDocumentFormatter */

/*! ./ConnectionEventsHandler/onDocumentSave */

/*! ./ConnectionEventsHandler/onInitializeConnection */

/*! ./ConnectionEventsHandler/onInitializedConnection */

/*! ./callHierarchy.proposed */

/*! ./cancellation */

/*! ./clone.js */

/*! ./colors */

/*! ./completionItemsRegex */

/*! ./configuration */

/*! ./constants */

/*! ./conversions */

/*! ./copy */

/*! ./copy-sync */

/*! ./deprecations */

/*! ./document-parse-features/workspace-files-parse.service */

/*! ./empty */

/*! ./ensure */

/*! ./events */

/*! ./features/clearComment */

/*! ./features/document-parse-language-features.service */

/*! ./features/function-scope-params-parse */

/*! ./file */

/*! ./files */

/*! ./fs */

/*! ./getCompletionItems */

/*! ./is */

/*! ./json */

/*! ./jsonfile */

/*! ./legacy-streams.js */

/*! ./levels */

/*! ./lib/colors */

/*! ./lib/constants */

/*! ./lib/err */

/*! ./lib/levels */

/*! ./lib/meta */

/*! ./lib/modifiers */

/*! ./lib/parse */

/*! ./lib/proto */

/*! ./lib/redaction */

/*! ./lib/redactor */

/*! ./lib/req */

/*! ./lib/res */

/*! ./lib/restorer */

/*! ./lib/rx */

/*! ./lib/state */

/*! ./lib/symbols */

/*! ./lib/time */

/*! ./lib/tools */

/*! ./lib/utils */

/*! ./lib/validator */

/*! ./link */

/*! ./linkedMap */

/*! ./make-dir */

/*! ./makeCodeForDebug */

/*! ./messageReader */

/*! ./messageWriter */

/*! ./messages */

/*! ./meta */

/*! ./mkdirs */

/*! ./modifiers */

/*! ./move */

/*! ./move-sync */

/*! ./output */

/*! ./output-json */

/*! ./output-json-sync */

/*! ./path-exists */

/*! ./pipeSupport */

/*! ./polyfills.js */

/*! ./progress */

/*! ./protocol */

/*! ./protocol.callHierarchy.proposed */

/*! ./protocol.colorProvider */

/*! ./protocol.configuration */

/*! ./protocol.declaration */

/*! ./protocol.foldingRange */

/*! ./protocol.implementation */

/*! ./protocol.progress */

/*! ./protocol.selectionRange */

/*! ./protocol.sematicTokens.proposed */

/*! ./protocol.typeDefinition */

/*! ./protocol.workspaceFolders */

/*! ./redaction */

/*! ./remove */

/*! ./rimraf */

/*! ./route */

/*! ./rx */

/*! ./sematicTokens.proposed */

/*! ./services/autocomplete.service */

/*! ./services/manager-receive-notification.service */

/*! ./services/on-hover.service */

/*! ./socketSupport */

/*! ./symbols */

/*! ./symlink */

/*! ./symlink-paths */

/*! ./symlink-type */

/*! ./templates */

/*! ./tools */

/*! ./util */

/*! ./utils */

/*! ./utils/is */

/*! ./utils/uuid */

/*! ./workspaceFolders */

/*! @hapi/bourne */

/*! ansi-styles */

/*! assert */

/*! at-least-node */

/*! atomic-sleep */

/*! chalk */

/*! child_process */

/*! color-convert */

/*! color-name */

/*! constants */

/*! crypto */

/*! dateformat */

/*! dotenv */

/*! eol */

/*! events */

/*! exports provided: Position, Range, Location, LocationLink, Color, ColorInformation, ColorPresentation, FoldingRangeKind, FoldingRange, DiagnosticRelatedInformation, DiagnosticSeverity, DiagnosticTag, Diagnostic, Command, TextEdit, TextDocumentEdit, CreateFile, RenameFile, DeleteFile, WorkspaceEdit, WorkspaceChange, TextDocumentIdentifier, VersionedTextDocumentIdentifier, TextDocumentItem, MarkupKind, MarkupContent, CompletionItemKind, InsertTextFormat, CompletionItemTag, CompletionItem, CompletionList, MarkedString, Hover, ParameterInformation, SignatureInformation, DocumentHighlightKind, DocumentHighlight, SymbolKind, SymbolTag, SymbolInformation, DocumentSymbol, CodeActionKind, CodeActionContext, CodeAction, CodeLens, FormattingOptions, DocumentLink, SelectionRange, EOL, TextDocument */

/*! exports provided: TextDocument */

/*! exports provided: URI, uriToFsPath */

/*! exports provided: name, version, description, main, browser, files, scripts, bin, precommit, repository, keywords, author, contributors, license, bugs, homepage, devDependencies, dependencies, default */

/*! fast-redact */

/*! fast-safe-stringify */

/*! fastify-warning */

/*! flatstr */

/*! fs */

/*! fs-extra */

/*! graceful-fs */

/*! has-flag */

/*! jmespath */

/*! jsonfile */

/*! jsonfile/utils */

/*! net */

/*! no static exports found */

/*! os */

/*! path */

/*! pino */

/*! pino-pretty */

/*! pino-std-serializers */

/*! quick-format-unescaped */

/*! rfdc */

/*! sonic-boom */

/*! stream */

/*! supports-color */

/*! tty */

/*! universalify */

/*! url */

/*! util */

/*! vm */

/*! vscode-jsonrpc */

/*! vscode-languageserver */

/*! vscode-languageserver-protocol */

/*! vscode-languageserver-textdocument */

/*! vscode-languageserver-types */

/*! vscode-uri */

/*!*********************!*\
  !*** external "fs" ***!
  \*********************/

/*!*********************!*\
  !*** external "os" ***!
  \*********************/

/*!*********************!*\
  !*** external "vm" ***!
  \*********************/

/*!**********************!*\
  !*** external "net" ***!
  \**********************/

/*!**********************!*\
  !*** external "tty" ***!
  \**********************/

/*!**********************!*\
  !*** external "url" ***!
  \**********************/

/*!***********************!*\
  !*** external "path" ***!
  \***********************/

/*!***********************!*\
  !*** external "util" ***!
  \***********************/

/*!*************************!*\
  !*** external "assert" ***!
  \*************************/

/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/

/*!*************************!*\
  !*** external "events" ***!
  \*************************/

/*!*************************!*\
  !*** external "stream" ***!
  \*************************/

/*!****************************!*\
  !*** external "constants" ***!
  \****************************/

/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/

/*!*********************************!*\
  !*** ./node_modules/eol/eol.js ***!
  \*********************************/

/*!**********************************!*\
  !*** ./src/lsp/server/server.ts ***!
  \**********************************/

/*!***********************************!*\
  !*** (webpack)/buildin/module.js ***!
  \***********************************/

/*!***********************************!*\
  !*** ./node_modules/pino/pino.js ***!
  \***********************************/

/*!************************************!*\
  !*** ./node_modules/rfdc/index.js ***!
  \************************************/

/*!**************************************!*\
  !*** ./src/Helpers/logger.helper.ts ***!
  \**************************************/

/*!***************************************!*\
  !*** ./node_modules/flatstr/index.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./node_modules/pino/lib/meta.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./node_modules/pino/lib/time.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/Helpers/regex-patterns.ts ***!
  \***************************************/

/*!****************************************!*\
  !*** ./node_modules/jsonfile/index.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./node_modules/jsonfile/utils.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./node_modules/pino/lib/proto.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./node_modules/pino/lib/tools.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./node_modules/pino/package.json ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./node_modules/dotenv/lib/main.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./node_modules/pino/lib/levels.js ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./node_modules/pino/lib/symbols.js ***!
  \******************************************/

/*!******************************************!*\
  !*** ./node_modules/sonic-boom/index.js ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./node_modules/fast-redact/index.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./node_modules/graceful-fs/clone.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./node_modules/jmespath/jmespath.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./node_modules/pino-pretty/index.js ***!
  \*******************************************/

/*!********************************************!*\
  !*** ./node_modules/atomic-sleep/index.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./node_modules/fast-redact/lib/rx.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./node_modules/fs-extra/lib/index.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./node_modules/pino/lib/redaction.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./node_modules/universalify/index.js ***!
  \********************************************/

/*!*********************************************!*\
  !*** ./node_modules/at-least-node/index.js ***!
  \*********************************************/

/*!***********************************************!*\
  !*** ./node_modules/fast-redact/lib/parse.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/fast-redact/lib/state.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/fastify-warning/index.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/fs-extra/lib/fs/index.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/graceful-fs/polyfills.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/pino-pretty/lib/utils.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/pino/lib/deprecations.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/is.js ***!
  \***********************************************/

/*!************************************************!*\
  !*** ./node_modules/@hapi/bourne/lib/index.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/fs-extra/lib/copy/copy.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/fs-extra/lib/move/move.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/fs-extra/lib/util/stat.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/pino-pretty/lib/colors.js ***!
  \************************************************/

/*!*************************************************!*\
  !*** ./node_modules/fs-extra/lib/copy/index.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./node_modules/fs-extra/lib/json/index.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./node_modules/fs-extra/lib/move/index.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./node_modules/graceful-fs/graceful-fs.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/main.js ***!
  \*************************************************/

/*!**************************************************!*\
  !*** ./node_modules/fast-redact/lib/redactor.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/fast-redact/lib/restorer.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/fs-extra/lib/empty/index.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/fs-extra/lib/ensure/file.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/fs-extra/lib/ensure/link.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/fs-extra/lib/util/utimes.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/vscode-uri/lib/esm/index.js ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ./node_modules/dateformat/lib/dateformat.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fast-redact/lib/modifiers.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fast-redact/lib/validator.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fast-safe-stringify/index.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fs-extra/lib/ensure/index.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fs-extra/lib/mkdirs/index.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fs-extra/lib/output/index.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/fs-extra/lib/remove/index.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/pino-pretty/lib/constants.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/events.js ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ./node_modules/fs-extra/lib/json/jsonfile.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/fs-extra/lib/remove/rimraf.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/graceful-fs/legacy-streams.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/pino-std-serializers/index.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./src/Helpers/FileSystem/FileSystemHelper.ts ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/fs-extra/lib/ensure/symlink.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/messages.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/lsp/server/services/on-hover.service.ts ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./node_modules/fs-extra/lib/copy-sync/index.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/fs-extra/lib/mkdirs/make-dir.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/fs-extra/lib/move-sync/index.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/err.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/req.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/pino-std-serializers/lib/res.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/quick-format-unescaped/index.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/linkedMap.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./src/lsp/server/services/formatter.service.ts ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/fs-extra/lib/json/output-json.js ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./node_modules/fs-extra/lib/path-exists/index.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/pipeSupport.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/main.js ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/cancellation.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/files.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/Actions/CodeDebugAction/makeCodeForDebug.ts ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/lsp/server/helper/create-document.helper.ts ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/lsp/server/services/autocomplete.service.ts ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/fs-extra/lib/copy-sync/copy-sync.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/fs-extra/lib/ensure/symlink-type.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/fs-extra/lib/move-sync/move-sync.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/messageReader.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/messageWriter.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/vscode-jsonrpc/lib/socketSupport.js ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/fs-extra/lib/ensure/symlink-paths.js ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./node_modules/fs-extra/lib/json/output-json-sync.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/progress.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/utils/is.js ***!
  \************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/utils/uuid.js ***!
  \**************************************************************/

/*!****************************************************************!*\
  !*** ./src/lsp/server/helper/filter-vba-files-paths.helper.ts ***!
  \****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/has-flag/index.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/main.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/configuration.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./src/Actions/CodeDebugAction/createAllCodeForDebugEvt.ts ***!
  \*****************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/vscode-languageserver-types/lib/esm/main.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./src/lsp/server/ConnectionEventsHandler/onDocumentSave.ts ***!
  \******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/color-name/index.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./src/lsp/server/helper/upade-language-parse-item.helper.ts ***!
  \*******************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/ansi-styles/index.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/chalk/source/util.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/workspaceFolders.js ***!
  \********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/chalk/source/index.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/pino/node_modules/fast-safe-stringify/index.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/messages.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/utils/is.js ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/color-convert/index.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/color-convert/route.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/getCompletionItems.ts ***!
  \**********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/supports-color/index.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./src/lsp/server/ConnectionEventsHandler/onDocumentFormatter.ts ***!
  \***********************************************************************/

/*!************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/completionItemsRegex.ts ***!
  \************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/chalk/source/templates.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-textdocument/lib/esm/main.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/features/clearComment.ts ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./src/lsp/server/services/manager-receive-notification.service.ts ***!
  \*************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/callHierarchy.proposed.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/vscode-languageserver/lib/sematicTokens.proposed.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./src/lsp/server/ConnectionEventsHandler/onInitializeConnection.ts ***!
  \**************************************************************************/

/*!***************************************************************************!*\
  !*** ./src/Actions/ImmediateWindowAction/deleteImmediateWindowDataEvt.ts ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./src/lsp/server/ConnectionEventsHandler/onInitializedConnection.ts ***!
  \***************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/pino-pretty/node_modules/color-convert/conversions.js ***!
  \****************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.progress.js ***!
  \******************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.declaration.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/workspace-files-parse.service.ts ***!
  \*********************************************************************************/

/*!**********************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.foldingRange.js ***!
  \**********************************************************************************/

/*!***********************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.colorProvider.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.configuration.js ***!
  \***********************************************************************************/

/*!***********************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/filter-parsed-language-features.ts ***!
  \***********************************************************************************/

/*!************************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.implementation.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.selectionRange.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.typeDefinition.js ***!
  \************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.workspaceFolders.js ***!
  \**************************************************************************************/

/*!****************************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/features/function-scope-params-parse.ts ***!
  \****************************************************************************************/

/*!****************************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/get-all-app-parsed-language-features.ts ***!
  \****************************************************************************************/

/*!********************************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.callHierarchy.proposed.js ***!
  \********************************************************************************************/

/*!********************************************************************************************!*\
  !*** ./node_modules/vscode-languageserver-protocol/lib/protocol.sematicTokens.proposed.js ***!
  \********************************************************************************************/

/*!*****************************************************************************************************!*\
  !*** ./src/lsp/server/document-parse-features/features/document-parse-language-features.service.ts ***!
  \*****************************************************************************************************/
