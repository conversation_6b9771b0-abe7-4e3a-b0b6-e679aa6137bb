/**
 * XVBA Extension Patch for VBA Import Bug Fix
 * 
 * This patch fixes the "ThisWorkbook" alphabetical ordering bug by intercepting
 * and modifying the import logic to process components in their natural order
 * instead of alphabetical order.
 */

const fs = require('fs');
const path = require('path');

class ExtensionPatcher {
    constructor() {
        this.extensionPath = path.join(__dirname, 'dist', 'extension.js');
        this.backupPath = path.join(__dirname, 'dist', 'extension.js.backup');
    }

    /**
     * Create a backup of the original extension
     */
    createBackup() {
        if (!fs.existsSync(this.backupPath)) {
            fs.copyFileSync(this.extensionPath, this.backupPath);
            console.log('✅ Backup created: extension.js.backup');
        } else {
            console.log('ℹ️  Backup already exists');
        }
    }

    /**
     * Restore from backup
     */
    restoreBackup() {
        if (fs.existsSync(this.backupPath)) {
            fs.copyFileSync(this.backupPath, this.extensionPath);
            console.log('✅ Extension restored from backup');
        } else {
            console.log('❌ No backup found');
        }
    }

    /**
     * Apply the patch to fix the VBA import bug
     */
    applyPatch() {
        try {
            // Read the compiled extension
            let extensionCode = fs.readFileSync(this.extensionPath, 'utf8');

            // Create the patch code that will fix the import logic
            const patchCode = `
// XVBA Import Bug Fix Patch
(function() {
    const originalSort = Array.prototype.sort;
    
    // Override Array.sort to detect VBA component sorting and fix it
    Array.prototype.sort = function(compareFn) {
        // Check if this looks like VBA component sorting
        if (this.length > 0 && 
            this[0] && 
            typeof this[0] === 'object' && 
            this[0].hasOwnProperty('name') && 
            this[0].hasOwnProperty('type')) {
            
            // Check if any component is "ThisWorkbook" - indicates VBA components
            const hasThisWorkbook = this.some(item => 
                item.name === 'ThisWorkbook' || 
                item.name === 'clsThisWorkbook'
            );
            
            if (hasThisWorkbook) {
                console.log('🔧 XVBA Patch: Detected VBA component sorting, applying fix...');
                
                // Sort by natural order (type first, then by index/order if available)
                return originalSort.call(this, (a, b) => {
                    // Prioritize by type: 100 (sheets/workbook) first, then 2 (classes), then 1 (modules)
                    if (a.type !== b.type) {
                        return b.type - a.type; // Higher type numbers first
                    }
                    
                    // Within same type, maintain original order or use index if available
                    if (a.index !== undefined && b.index !== undefined) {
                        return a.index - b.index;
                    }
                    
                    // Fallback to name comparison, but ensure ThisWorkbook comes early
                    if (a.name === 'ThisWorkbook') return -1;
                    if (b.name === 'ThisWorkbook') return 1;
                    
                    return a.name.localeCompare(b.name);
                });
            }
        }
        
        // For non-VBA arrays, use original sort
        return originalSort.call(this, compareFn);
    };
    
    console.log('✅ XVBA Import Bug Fix Patch Applied');
})();
`;

            // Insert the patch at the beginning of the extension
            const patchedCode = patchCode + '\n' + extensionCode;

            // Write the patched extension
            fs.writeFileSync(this.extensionPath, patchedCode, 'utf8');
            
            console.log('✅ Patch applied successfully!');
            console.log('📝 The extension will now process VBA components in their natural order');
            console.log('🔄 Please reload VSCode to activate the patch');
            
            return true;
        } catch (error) {
            console.error('❌ Failed to apply patch:', error.message);
            return false;
        }
    }

    /**
     * Check if the patch is already applied
     */
    isPatchApplied() {
        try {
            const extensionCode = fs.readFileSync(this.extensionPath, 'utf8');
            return extensionCode.includes('XVBA Import Bug Fix Patch');
        } catch (error) {
            return false;
        }
    }

    /**
     * Remove the patch
     */
    removePatch() {
        if (fs.existsSync(this.backupPath)) {
            this.restoreBackup();
            console.log('✅ Patch removed, extension restored to original state');
        } else {
            console.log('❌ Cannot remove patch: no backup found');
        }
    }
}

// Command line interface
if (require.main === module) {
    const patcher = new ExtensionPatcher();
    const command = process.argv[2];

    switch (command) {
        case 'apply':
            console.log('🔧 Applying XVBA Import Bug Fix Patch...\n');
            patcher.createBackup();
            if (patcher.isPatchApplied()) {
                console.log('ℹ️  Patch is already applied');
            } else {
                patcher.applyPatch();
            }
            break;

        case 'remove':
            console.log('🗑️  Removing XVBA Import Bug Fix Patch...\n');
            patcher.removePatch();
            break;

        case 'status':
            console.log('📊 XVBA Patch Status:\n');
            console.log(`Patch applied: ${patcher.isPatchApplied() ? '✅ Yes' : '❌ No'}`);
            console.log(`Backup exists: ${fs.existsSync(patcher.backupPath) ? '✅ Yes' : '❌ No'}`);
            break;

        default:
            console.log(`
🔧 XVBA Extension Patcher

Usage:
  node extension_patch.js apply   - Apply the import bug fix patch
  node extension_patch.js remove  - Remove the patch and restore original
  node extension_patch.js status  - Check patch status

Description:
  This patch fixes the VBA import bug where class modules alphabetically 
  after "ThisWorkbook" were skipped during import. The patch modifies the 
  sorting logic to process components in their natural order instead of 
  alphabetical order.

Note:
  - A backup of the original extension is created automatically
  - You need to reload VSCode after applying/removing the patch
  - This is a runtime patch that doesn't require source code access
            `);
    }
}

module.exports = ExtensionPatcher;
