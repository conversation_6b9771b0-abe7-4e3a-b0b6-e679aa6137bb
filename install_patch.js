/**
 * XVBA Import Bug Fix - One-Click Installer
 * 
 * This script downloads and applies the XVBA import bug fix patch.
 * Can be run from any directory - automatically handles everything.
 */

const fs = require('fs');
const path = require('path');

// Embed the patch code directly in this installer
const PATCH_CODE = `/**
 * XVBA Extension Patch for VBA Import Bug Fix
 * 
 * This patch fixes the "ThisWorkbook" alphabetical ordering bug by intercepting
 * and modifying the import logic to process components in their natural order
 * instead of alphabetical order.
 * 
 * Can be run from any directory - automatically detects VSCode extension location.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class ExtensionPatcher {
    constructor() {
        this.extensionDir = this.findExtensionDirectory();
        if (!this.extensionDir) {
            throw new Error('XVBA extension not found. Please ensure it is installed.');
        }
        
        this.extensionPath = path.join(this.extensionDir, 'dist', 'extension.js');
        this.backupPath = path.join(this.extensionDir, 'dist', 'extension.js.backup');
        
        console.log(\`📍 Found XVBA extension at: \${this.extensionDir}\`);
    }

    findExtensionDirectory() {
        const possiblePaths = this.getPossibleExtensionPaths();
        
        for (const extensionPath of possiblePaths) {
            if (this.isValidExtensionDirectory(extensionPath)) {
                return extensionPath;
            }
        }
        
        return null;
    }

    getPossibleExtensionPaths() {
        const userHome = os.homedir();
        const extensionName = 'local-smart.excel-live-server-4.0.30';
        const paths = [];

        if (process.platform === 'win32') {
            paths.push(
                path.join(userHome, '.vscode', 'extensions', extensionName),
                path.join(userHome, 'AppData', 'Roaming', 'Code', 'User', 'extensions', extensionName),
                path.join(userHome, 'AppData', 'Local', 'Programs', 'Microsoft VS Code', 'resources', 'app', 'extensions', extensionName),
                path.join(userHome, '.vscode-insiders', 'extensions', extensionName),
                path.join(userHome, 'AppData', 'Roaming', 'Code - Insiders', 'User', 'extensions', extensionName)
            );
        } else if (process.platform === 'darwin') {
            paths.push(
                path.join(userHome, '.vscode', 'extensions', extensionName),
                path.join(userHome, 'Library', 'Application Support', 'Code', 'User', 'extensions', extensionName),
                path.join(userHome, '.vscode-insiders', 'extensions', extensionName),
                path.join(userHome, 'Library', 'Application Support', 'Code - Insiders', 'User', 'extensions', extensionName)
            );
        } else {
            paths.push(
                path.join(userHome, '.vscode', 'extensions', extensionName),
                path.join(userHome, '.config', 'Code', 'User', 'extensions', extensionName),
                path.join(userHome, '.vscode-insiders', 'extensions', extensionName),
                path.join(userHome, '.config', 'Code - Insiders', 'User', 'extensions', extensionName)
            );
        }

        const versionVariants = [
            'local-smart.excel-live-server-4.0.30',
            'local-smart.excel-live-server-4.0.29',
            'local-smart.excel-live-server-4.0.31',
            'local-smart.excel-live-server-4.0.28'
        ];

        const allPaths = [];
        for (const basePath of paths) {
            const baseDir = path.dirname(basePath);
            for (const variant of versionVariants) {
                allPaths.push(path.join(baseDir, variant));
            }
        }

        if (this.isValidExtensionDirectory(process.cwd())) {
            allPaths.unshift(process.cwd());
        }

        return allPaths;
    }

    isValidExtensionDirectory(dirPath) {
        try {
            const packageJsonPath = path.join(dirPath, 'package.json');
            const extensionJsPath = path.join(dirPath, 'dist', 'extension.js');
            
            if (!fs.existsSync(packageJsonPath) || !fs.existsSync(extensionJsPath)) {
                return false;
            }

            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            return packageJson.name === 'excel-live-server' && 
                   packageJson.publisher === 'local-smart';
        } catch (error) {
            return false;
        }
    }

    createBackup() {
        if (!fs.existsSync(this.extensionPath)) {
            throw new Error(\`Extension file not found: \${this.extensionPath}\`);
        }
        
        if (!fs.existsSync(this.backupPath)) {
            fs.copyFileSync(this.extensionPath, this.backupPath);
            console.log('✅ Backup created: extension.js.backup');
        } else {
            console.log('ℹ️  Backup already exists');
        }
    }

    applyPatch() {
        try {
            let extensionCode = fs.readFileSync(this.extensionPath, 'utf8');

            const patchCode = \`
// XVBA Import Bug Fix Patch
(function() {
    const originalSort = Array.prototype.sort;
    
    Array.prototype.sort = function(compareFn) {
        if (this.length > 0 && 
            this[0] && 
            typeof this[0] === 'object' && 
            this[0].hasOwnProperty('name') && 
            this[0].hasOwnProperty('type')) {
            
            const hasThisWorkbook = this.some(item => 
                item.name === 'ThisWorkbook' || 
                item.name === 'clsThisWorkbook'
            );
            
            if (hasThisWorkbook) {
                console.log('🔧 XVBA Patch: Detected VBA component sorting, applying fix...');
                
                return originalSort.call(this, (a, b) => {
                    if (a.type !== b.type) {
                        return b.type - a.type;
                    }
                    
                    if (a.index !== undefined && b.index !== undefined) {
                        return a.index - b.index;
                    }
                    
                    if (a.name === 'ThisWorkbook') return -1;
                    if (b.name === 'ThisWorkbook') return 1;
                    
                    return a.name.localeCompare(b.name);
                });
            }
        }
        
        return originalSort.call(this, compareFn);
    };
    
    console.log('✅ XVBA Import Bug Fix Patch Applied');
})();
\`;

            const patchedCode = patchCode + '\\n' + extensionCode;
            fs.writeFileSync(this.extensionPath, patchedCode, 'utf8');
            
            console.log('✅ Patch applied successfully!');
            console.log('📝 The extension will now process VBA components in their natural order');
            console.log('🔄 Please reload VSCode to activate the patch');
            
            return true;
        } catch (error) {
            console.error('❌ Failed to apply patch:', error.message);
            return false;
        }
    }

    isPatchApplied() {
        try {
            const extensionCode = fs.readFileSync(this.extensionPath, 'utf8');
            return extensionCode.includes('XVBA Import Bug Fix Patch');
        } catch (error) {
            return false;
        }
    }
}

module.exports = ExtensionPatcher;
`;

function createPatchFile() {
    const patchFileName = 'extension_patch.js';
    
    console.log('📦 Creating patch file...');
    fs.writeFileSync(patchFileName, PATCH_CODE, 'utf8');
    console.log(`✅ Created: ${patchFileName}`);
    
    return patchFileName;
}

function runPatch() {
    try {
        console.log('🔧 XVBA Import Bug Fix - One-Click Installer\n');
        
        // Create the patch file
        const patchFile = createPatchFile();
        
        // Load and run the patcher
        const ExtensionPatcher = require(`./${patchFile}`);
        const patcher = new ExtensionPatcher();
        
        // Apply the patch
        console.log('🔧 Applying patch...\n');
        patcher.createBackup();
        
        if (patcher.isPatchApplied()) {
            console.log('ℹ️  Patch is already applied');
        } else {
            patcher.applyPatch();
        }
        
        console.log('\n🎉 Installation complete!');
        console.log('\n📋 Next steps:');
        console.log('  1. Reload VSCode (Ctrl+Shift+P → "Developer: Reload Window")');
        console.log('  2. Test VBA import functionality');
        console.log('  3. Check that all class modules are imported (not just those before "ThisWorkbook")');
        
    } catch (error) {
        console.error('❌ Installation failed:', error.message);
        console.log('\n💡 Troubleshooting:');
        console.log('  1. Ensure XVBA extension is installed in VSCode');
        console.log('  2. Try running the patch manually: node extension_patch.js apply');
        console.log('  3. Check if VSCode is installed in a custom location');
        process.exit(1);
    }
}

// Run the installer
if (require.main === module) {
    runPatch();
}

module.exports = { createPatchFile, runPatch };
