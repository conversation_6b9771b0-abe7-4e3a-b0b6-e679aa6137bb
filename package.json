{"name": "excel-live-server", "displayName": "XVBA - Live Server VBA", "description": "Edit VBA files from Excel/Access In VSCode with Intellisense,Live Server, and Create Ribbons. You can use Snippets, Autocomplete (Language Server), run Macros from VSCode, add namespace to your vba files etc..  ", "author": "<PERSON>", "email": "<EMAIL>", "license": "MIT", "version": "4.0.30", "publisher": "local-smart", "repository": {"type": "git", "url": "https://github.com/aeraphe/excel-autoload"}, "engines": {"vscode": "^1.59.0"}, "categories": ["Programming Languages", "Snippets", "Language Packs", "Formatters", "Debuggers", "Testing", "Visualization", "Other"], "keywords": ["Xvba", "vba", "excel", "live Server", "vb", "vbs", "macro"], "icon": "resources/icon.png", "activationEvents": ["*"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "xvba.formatter", "title": "XVBA Format Code"}, {"command": "excel-live-server.up_vba_to_excel", "title": "Export VBA", "icon": {"light": "resources/vba_2_excel.svg", "dark": "resources/vba_2_excel.svg"}}, {"command": "excel-live-server.bootstrap", "title": "Bootstrap XVBA Config", "icon": {"light": "resources/bootstrap.svg", "dark": "resources/bootstrap.svg"}}, {"command": "excel-live-server.run_macro", "title": "<PERSON>", "icon": {"light": "resources/play1.svg", "dark": "resources/play1.svg"}}, {"command": "excel-live-server.autoloadexcel", "title": "Start XVBA Live Server", "icon": {"light": "resources/run.svg", "dark": "resources/run.svg"}}, {"command": "excel-live-server.import_vba", "title": "Import - VBA", "icon": {"light": "resources/import.svg", "dark": "resources/import.svg"}}, {"command": "excel-live-server.stop_live_server", "title": "Stop XVBA Live Server", "icon": {"light": "resources/stop.svg", "dark": "resources/stop.svg"}}, {"command": "xvba.save_ribbon", "title": "Add Custom Ribbon Menu", "icon": {"light": "resources/menu_ribbon.svg", "dark": "resources/menu_ribbon.svg"}}, {"command": "vbaMacros.refreshEntry", "title": "Ref<PERSON>", "icon": {"light": "resources/light/refresh.svg", "dark": "resources/dark/refresh.svg"}}, {"command": "microsoft.open", "title": "Open MS Office", "icon": {"light": "resources/light/msoffice.svg", "dark": "resources/dark/msoffice.svg"}}], "configuration": {"type": "object", "title": "Example configuration", "properties": {"languageServerVBA.maxNumberOfProblems": {"scope": "resource", "type": "number", "default": 100, "description": "Controls the maximum number of problems produced by the server."}, "languageServerVBA.trace.server": {"scope": "window", "type": "string", "enum": ["off", "messages", "verbose"], "default": "off", "description": "Traces the communication between VS Code and the language server."}}}, "languages": [{"id": "vb", "aliases": ["Visual Basic Application", "VBA", "Excel", "Access", "VBS", "CATScript"], "extensions": [".cls", ".frm", ".bas", ".vbs", ".catscript"], "configuration": "./language-configuration.json", "grammars": [{"language": "vbscript", "scopeName": "source.vbs", "path": "./syntaxes/vbscript.json"}]}], "views": {"explorer": [{"id": "vbaMacros", "name": "XVBA - Macro List"}]}, "menus": {"view/title": [{"command": "excel-live-server.bootstrap", "when": "view == vbaMacros", "group": "navigation"}, {"command": "excel-live-server.import_vba", "when": "view == vbaMacros", "group": "navigation@1"}, {"command": "excel-live-server.stop_live_server", "when": "view == vbaMacros", "group": "navigation@2"}, {"command": "excel-live-server.up_vba_to_excel", "when": "view == vbaMacros", "group": "navigation@3"}, {"command": "excel-live-server.autoloadexcel", "when": "view == vbaMacros", "group": "navigation@4"}, {"command": "xvba.save_ribbon", "when": "view == vbaMacros", "group": "navigation@5"}, {"command": "vbaMacros.refreshEntry", "when": "view == vbaMacros", "group": "navigation@7"}, {"command": "microsoft.open", "when": "view == vbaMacros", "group": "navigation@6"}], "view/item/context": [{"command": "excel-live-server.run_macro", "when": "view == vbaMacros", "group": "inline"}]}}, "scripts": {"vscode:prepublish2": "npm run compile", "compile": "tsc -p ./", "lint": "eslint src --ext ts", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "test": "node ./out/test/runTest.js", "start": " npx gulp autoload-excel", "import": "npx gulp import-vba-excel", "vscode:prepublish": "webpack --mode production", "webpack": "webpack --mode development", "wdev": "webpack --mode development --watch", "test-compile": "tsc -p ./"}, "devDependencies": {"@types/fs-extra": "^9.0.12", "@types/glob": "^7.1.4", "@types/mocha": "^7.0.2", "@types/vscode": "^1.59.0", "@typescript-eslint/eslint-plugin": "^2.34.0", "@typescript-eslint/parser": "^2.34.0", "eslint": "^6.8.0", "glob": "^7.1.7", "mocha": "^7.2.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^7.0.5", "typescript": "^3.9.10", "vsce": "^1.96.1", "vscode-test": "^1.6.1", "webpack": "^4.46.0", "webpack-cli": "^3.3.12"}, "dependencies": {"adm-zip": "^0.5.5", "chokidar": "^3.5.2", "dotenv": "^8.6.0", "eol": "^0.9.1", "fs-extra": "^9.1.0", "lodash": "^4.17.21", "logger": "0.0.1", "mv": "^2.1.1", "node-localstorage": "^2.2.1", "node-watch": "^0.6.4", "original-fs": "^1.1.0", "pino": "^6.13.1", "pino-pretty": "^4.8.0", "read-last-lines": "^1.8.0", "replace": "^1.2.1", "semver": "^7.3.5", "to-utf-8": "^1.3.0", "vscode-languageclient": "^6.1.4", "vscode-languageserver": "^6.1.1", "vscode-languageserver-textdocument": "^1.0.1", "vscode-uri": "^2.1.2", "winston": "^3.3.3", "write-file-utf8": "^2.0.1", "xml2js": "^0.4.23", "xml2json": "^0.12.0"}, "__metadata": {"id": "03b4aa98-7a2a-4a44-a8ea-41d65519701d", "publisherId": "5450c86c-88b1-4d55-b1a5-134615981817", "publisherDisplayName": "Local Smart", "targetPlatform": "undefined", "isApplicationScoped": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "installedTimestamp": 1726550083319, "pinned": false, "preRelease": false, "source": "gallery", "size": 1701046}}