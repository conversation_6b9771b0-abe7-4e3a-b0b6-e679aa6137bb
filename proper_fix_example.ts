/**
 * PROPER FIX: VBA Import Logic (TypeScript Example)
 * 
 * This shows what the proper fix would look like in the original source code.
 * This would replace the buggy import logic in the VSCode extension.
 */

interface VBAComponent {
    name: string;
    type: number;  // 1 = .bas, 2 = .cls, 100 = sheet/workbook objects
    index?: number;
    content?: string;
    path?: string;
}

class VBAImporter {
    /**
     * FIXED: Import VBA components in their natural order
     * This replaces the buggy alphabetical sorting logic
     */
    public async importVBAComponents(components: VBAComponent[]): Promise<void> {
        console.log(`🔄 Starting VBA import for ${components.length} components...`);
        
        // FIXED: Sort by Excel's natural order instead of alphabetical
        const sortedComponents = this.sortComponentsByNaturalOrder(components);
        
        let importedCount = 0;
        const errors: string[] = [];
        
        // Process ALL components - no premature exit
        for (const component of sortedComponents) {
            try {
                console.log(`📥 Importing: ${component.name} (Type: ${component.type})`);
                
                await this.importSingleComponent(component);
                importedCount++;
                
                console.log(`  ✅ Successfully imported: ${component.name}`);
                
                // REMOVED: No premature exit when encountering "ThisWorkbook"
                // The original bug was here - it would break the loop
                
            } catch (error) {
                const errorMsg = `Failed to import ${component.name}: ${error.message}`;
                console.error(`  ❌ ${errorMsg}`);
                errors.push(errorMsg);
            }
        }
        
        // Report results
        console.log(`\n📊 Import Summary:`);
        console.log(`  ✅ Successfully imported: ${importedCount}/${components.length} components`);
        
        if (errors.length > 0) {
            console.log(`  ❌ Errors: ${errors.length}`);
            errors.forEach(error => console.log(`    - ${error}`));
        }
        
        if (importedCount === components.length) {
            console.log(`🎉 All VBA components imported successfully!`);
        }
    }

    /**
     * Sort VBA components in Excel's natural order
     * This is the key fix - proper ordering instead of alphabetical
     */
    private sortComponentsByNaturalOrder(components: VBAComponent[]): VBAComponent[] {
        return [...components].sort((a, b) => {
            // Primary sort: by component type
            // 100 = Excel objects (sheets, workbook) - should come first
            // 2 = Class modules - should come second  
            // 1 = Standard modules - should come last
            if (a.type !== b.type) {
                return b.type - a.type; // Higher type numbers first
            }
            
            // Secondary sort: within same type, maintain Excel's order
            if (a.index !== undefined && b.index !== undefined) {
                return a.index - b.index;
            }
            
            // Tertiary sort: special handling for Excel objects
            if (a.type === 100) {
                // Ensure ThisWorkbook comes after sheets
                if (a.name === 'ThisWorkbook' && b.name.startsWith('Sheet')) return 1;
                if (b.name === 'ThisWorkbook' && a.name.startsWith('Sheet')) return -1;
            }
            
            // Final fallback: alphabetical (but this rarely matters with proper indexing)
            return a.name.localeCompare(b.name);
        });
    }

    /**
     * Import a single VBA component
     */
    private async importSingleComponent(component: VBAComponent): Promise<void> {
        // Determine file extension based on component type
        const extension = this.getFileExtension(component.type);
        const fileName = `${component.name}${extension}`;
        
        // Create the file content with proper VBA formatting
        const fileContent = this.formatVBAContent(component);
        
        // Write the file to the appropriate directory
        await this.writeVBAFile(fileName, fileContent, component);
    }

    /**
     * Get the appropriate file extension for a VBA component type
     */
    private getFileExtension(type: number): string {
        switch (type) {
            case 1: return '.bas';  // Standard modules
            case 2: return '.cls';  // Class modules
            case 3: return '.frm';  // Forms
            case 100: return '.cls'; // Excel objects (sheets, workbook)
            default: return '.bas';
        }
    }

    /**
     * Format VBA content with proper headers and attributes
     */
    private formatVBAContent(component: VBAComponent): string {
        let content = '';
        
        // Add VBA headers based on component type
        if (component.type === 2 || component.type === 100) {
            // Class module header
            content += 'VERSION 1.0 CLASS\n';
            content += 'BEGIN\n';
            content += 'MultiUse = -1  \'True\n';
            content += 'END\n';
            content += `Attribute VB_Name = "${component.name}"\n`;
            content += 'Attribute VB_GlobalNameSpace = False\n';
            content += 'Attribute VB_Creatable = False\n';
            content += 'Attribute VB_PredeclaredId = False\n';
            content += 'Attribute VB_Exposed = False\n\n';
        } else {
            // Standard module header
            content += `Attribute VB_Name = "${component.name}"\n\n`;
        }
        
        // Add the actual VBA code
        content += component.content || '';
        
        return content;
    }

    /**
     * Write VBA file to the file system
     */
    private async writeVBAFile(fileName: string, content: string, component: VBAComponent): Promise<void> {
        // Implementation would write to the appropriate directory
        // This is where the actual file I/O happens
        console.log(`  📝 Writing file: ${fileName}`);
        
        // In the real implementation, this would:
        // 1. Determine the correct output directory based on namespace
        // 2. Create directories if they don't exist
        // 3. Write the file with proper encoding (Windows-1252)
        // 4. Handle any file system errors
    }
}

/**
 * COMPARISON: Original Buggy Logic vs Fixed Logic
 */
class BugComparison {
    /**
     * BUGGY: Original logic that caused the import failures
     */
    static buggyImportLogic(components: VBAComponent[]): VBAComponent[] {
        // BUG 1: Alphabetical sorting instead of natural order
        const sortedComponents = components.sort((a, b) => a.name.localeCompare(b.name));
        
        const imported: VBAComponent[] = [];
        
        for (const component of sortedComponents) {
            imported.push(component);
            
            // BUG 2: Premature exit when encountering "ThisWorkbook"
            if (component.name === "ThisWorkbook") {
                break; // This is the critical bug!
            }
        }
        
        return imported; // Missing components after "ThisWorkbook"
    }

    /**
     * FIXED: Proper logic that imports all components
     */
    static fixedImportLogic(components: VBAComponent[]): VBAComponent[] {
        // FIX 1: Sort by natural order (type, then index)
        const sortedComponents = components.sort((a, b) => {
            if (a.type !== b.type) return b.type - a.type;
            if (a.index !== undefined && b.index !== undefined) return a.index - b.index;
            return a.name.localeCompare(b.name);
        });
        
        const imported: VBAComponent[] = [];
        
        // FIX 2: Process ALL components, no premature exit
        for (const component of sortedComponents) {
            imported.push(component);
            // No break statement - continue processing all components
        }
        
        return imported; // All components imported successfully
    }
}

export { VBAImporter, VBAComponent, BugComparison };
