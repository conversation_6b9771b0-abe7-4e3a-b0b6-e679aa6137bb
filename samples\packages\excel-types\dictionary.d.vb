


'/*
'
'
'
'/*
Public Class Scripting()


'/*
'
'Object that stores data key/item pairs.
'
'/*
Public Function Dictionary()

End Function

'/*
'
'Adds a new key/item pair to a Dictionary object.
'
'*/
Public Sub Add() 

End Sub

'/*
'
'Returns a Boolean value that indicates whether a specified key exists in the Dictionary object.
'
'*/
Public Sub Exists() 

End Sub

'/*
'
'Returns an array of all the items in a Dictionary object.
'
'*/
Public Sub Items() 

End Sub

'/*
'
'Returns an array of all the keys in a Dictionary object.
'
'*/
Public Sub Keys() 

End Sub

'/*
'
'Removes one specified key/item pair from the Dictionary object.
'
'*/
Public Sub Remove() 

End Sub

'/*
'
'Removes all the key/item pairs in the Dictionary object.
'
'*/
Public Sub RemoveAll() 

End Sub
    
    

'/*
'Sets or returns the comparison mode for comparing keys in a Dictionary object.
'
'
'*/
Public Property CompareMode As Variant


'/*
'Returns the number of key/item pairs in a Dictionary object.
'
'
'*/
Public Property Count As Long


'/*
'Sets or returns the value of an item in a Dictionary object.
'
'
'*/
Public Property Item As Variant

'/*
'Sets a new key value for an existing key value in a Dictionary object.
'
'
'*/
Public Property Key As Variant



End Class