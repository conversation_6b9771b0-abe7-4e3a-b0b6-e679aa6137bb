
'/*
'[Conversion Func]
'
'*/
Function Asc()
End Function

'/*
'[Conversion Func]
'Returns a String containing the character associated 
'with the specified character code.
'
'Example:
'
'Dim MyChar
'MyChar = Chr(65)    ' Returns A.
'MyChar = Chr(97)    ' Returns a.
'MyChar = Chr(62)    ' Returns >.
'MyChar = Chr(37)    ' Returns %.
'*/
Function Chr()
End Function

'/*
'[Conversion Func]
'
'*/
Function Format()
End Function

'/*
'[Conversion Func]
'
'*/
Function Hex()
End Function


'/*
'[Conversion Func]
'
'*/
Function Oct()
End Function


'/*
'[Conversion Func]
'Returns a Variant (String) representation of a number.
'
'Example:
'Dim MyString
'MyString = Str(459)    ' Returns " 459".
'MyString = Str(-459.65)    ' Returns "-459.65".
'MyString = Str(459.001)    ' Returns " 459.001".
'
'*/
Function Str() As Variant
End Function


'/*
'[Conversion Func]
'Returns the numbers contained in a string as 
'a numeric value of appropriate type.
'
'Example:
'
'Dim MyValue
'MyValue = Val("2457")    ' Returns 2457.
'MyValue = Val(" 2 45 7")    ' Returns 2457.
'MyValue = Val("24 and 57")    ' Returns 24.
'*/
Function Val(String) 
End Function



Function CreateObject()
End Function


Function CurDir()
End Function

'/*
'Returns a Variant (Date) containing the current system date.
'
'Example
'
'Dim MyDate
'MyDate = Date    ' MyDate contains the current system date.
'
'*/    
Function Date() 

End Function



Function DateDiff()
End Function

Function DatePart()
End Function

Function DateSerial()
End Function

Function Day()
End Function

Function DDB()
End Function

Function Dir()
End Function

Function DoEvents()
End Function

Function Environ()
End Function

Function EOF()
End Function

Function Error()
End Function


Function FileAttr()
End Function

Function FileDateTime()
End Function

Function FileLen()
End Function

Function Filter()
End Function

Function FormatCurrency()
End Function

Function FormatDateTime()
End Function

Function FormatNumber()
End Function

Function FormaPercent()
End Function


Function FreeFile()
End Function

Function FV()
End Function

Function GetAllSettings()
End Function

Function GetAttr()
End Function

Function GetObject()
End Function

Function GetSetting()
End Function


Function Hour()
End Function

Function IIf()
End Function

Function IMEStatus()
End Function

Function Input()
End Function

Function InputBox()
End Function

Function InStr()
End Function

Function InStrRev()
End Function

Function IPmt()
End Function



Function IRR()
End Function

Function IsArray()
End Function

Function IsDate()
End Function

'/*
'
'Returns a Boolean value indicating whether a variable has been initialized.
'
'*/
Function IsEmpty()
End Function


Function IsError()
End Function

Function IsMissing()
End Function

Function IsNull()
End Function

Function IsNumeric()
End Function


Function IsObject()
End Function

'/*
'Returns a string created by joining a number of substrings contained in an array.
'
'*/
Function Join()
End Function



Function LBound()
End Function

Function LCase()
End Function

Function Left()
End Function

Function Len()
End Function


Function Loc()
End Function

Function LOF()
End Function

Function LTrim()
End Function

Function RTrim()
End Function

Function Trim()
End Function


Function MacID()
End Function



Function MacScript()
End Function

'/*
'[Math Function]
'Returns a value of the same type that is passed to it specifying the absolute value of a number.
'
'*/
Function Abs()
End Function

'/*
'[Math Function]
'Returns a Double specifying the arctangent of a number.
'
'*/
Function Atn()
End Function


'/*
'[Math Function]
'
'*/
Function Cos()
End Function

'/*
'[Math Function]
'
'*/
Function Exp()
End Function

'/*
'[Math Function]
'
'*/
Function Int()
End Function

'/*
'[Math Function]
'
'*/
Function Fix()
End Function

'/*
'[Math Function]
'
'*/ 
Function Log()
End Function


'/*
'[Math Function]
'
'*/
Function Rnd()
End Function

'/*
'[Math Function]
'
'*/
Function Sgn()
End Function

'/*
'[Math Function]
'
'*/
Function Sin()
End Function

'/*
'[Math Function]
'
'*/
Function Sqr()
End Function

'/*
'[Math Function]
'
'*/
Function Tan()
End Function



Function Mid()
End Function


Function Minute()
End Function


Function MIRR()
End Function


Function Month()
End Function

Function MonthName()
End Function

'/*
'Displays a message in a dialog box, waits for the user to click a button, 
'and returns an Integer indicating which button the user clicked.
'
'*/
Function MsgBox(prompt, [ buttons, ] [ title, ] [ helpfile, context ])
End Function


Function Now()
End Function


Function NPer()
End Function


Function NPV()
End Function

Function PPmt()
End Function


Function QBColor()
End Function


Function Rate()
End Function

'/*
'Returns a string, which is a substring of a string 
'expression beginning at the start position (defaults to 1), 
'in which a specified substring has been replaced with another 
'substring a specified number of times.
'
'*/
Function Replace(expression, find, replace, [ start, [ count, [ compare ]]])
End Function

Function RGB()
End Function


Function Right()
End Function


Function Round()
End Function


Function Second()
End Function



Function Seek()
End Function


Function Shell()
End Function


Function SNL()
End Function


Function Space()
End Function


Function Spc()
End Function


Function Split()
End Function


Function StrComp()
End Function



Function StrConv()
End Function


Function String()
End Function


Function StrReverse()
End Function



Function Switch()
End Function


Function SYD()
End Function


Function Tab()
End Function


Function Time()
End Function



Function Timer()
End Function


Function TimeSerial()
End Function


Function TimeValue()
End Function



Function TypeName()
End Function


Function UBound()
End Function


Function Ucase()
End Function


Function VarType()
End Function



Function Weekday()
End Function


Function WeekdayName()
End Function


Function Year()
End Function